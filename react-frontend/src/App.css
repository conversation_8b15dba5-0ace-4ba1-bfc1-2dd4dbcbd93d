/* === Import Inter Font === */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* === Global Resets & Base === */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* === THEME VARIABLES === */
body.light-theme {
  --primary-green: #00D188;
  --primary-green-darker: #00B374;
  --primary-green-lighter-bg: #e6fff5;

  --text-color-primary: #121212; /* Main text */
  --text-color-secondary: #555555; /* Softer text */
  --text-color-on-dark-bg: #F5F5F7; /* Text for dark solid backgrounds like buttons */
  --text-color-on-primary-bg: #121212; /* Text on primary green buttons */

  --bg-page: #FFFFFF;
  --bg-content-card: #FFFFFF;
  --bg-header-footer: #1C1C1E; /* Dark header/footer for contrast */
  --bg-code-block: #1C1C1E;
  --bg-metric-item: #f9f9f9;
  --bg-input: #FFFFFF;

  --border-primary: #D1D1D6; /* Standard borders */
  --border-strong: #B0B0B0; /* Stronger borders */
  --border-focus-ring: rgba(0, 209, 136, 0.25); /* Green focus */

  --button-primary-bg: var(--primary-green);
  --button-primary-text: var(--text-color-on-primary-bg);
  --button-primary-hover-bg: var(--primary-green-darker);

  --button-secondary-bg: var(--bg-content-card);
  --button-secondary-text: var(--primary-green);
  --button-secondary-border: var(--primary-green);
  --button-secondary-hover-bg: rgba(0, 209, 136, 0.05);

  --button-danger-bg: #721C24; /* Error text color used as bg */
  --button-danger-text: #FFFFFF;
  --button-danger-hover-bg: #5a1016;

  --message-success-bg: var(--primary-green-lighter-bg);
  --message-success-text: #006432;
  --message-success-border: #a3d3b1;
  --message-success-accent: var(--primary-green);

  --message-warning-bg: #FFF3CD;
  --message-warning-text: #856404;
  --message-warning-border: #FFECB3;

  --message-error-bg: #F8D7DA;
  --message-error-text: #721C24;
  --message-error-border: #F5C6CB;

  --message-info-bg: var(--primary-green-lighter-bg); /* Similar to success */
  --message-info-text: var(--primary-green-darker);
  --message-info-border: var(--primary-green);

  --link-color: var(--primary-green-darker);
  --link-hover-color: #008a5e;

  --shadow-card: 0 1px 3px rgba(0,0,0,0.04);
  --shadow-button: 0 2px 4px rgba(0,0,0,0.05);
  --shadow-button-hover: 0 4px 8px rgba(0,0,0,0.1);

  --checkbox-accent: var(--primary-green);
  --spinner-color: var(--primary-green);
}

body.dark-theme {
  --primary-green: #00E090; /* Slightly brighter green for dark mode */
  --primary-green-darker: #00C27A;
  --primary-green-lighter-bg: #1a3a30; /* Darker green bg */

  --text-color-primary: #EAEAEA;
  --text-color-secondary: #b0b0b0;
  --text-color-on-dark-bg: #EAEAEA; /* Light text for dark backgrounds like code blocks */
  --text-color-on-primary-bg: #121212; /* Dark text on primary green buttons */

  --bg-page: #121212; /* Very dark page */
  --bg-content-card: #1E1E1E; /* Cards slightly lighter */
  --bg-header-footer: #171717; /* Even darker header/footer */
  --bg-code-block: #171717;
  --bg-metric-item: #252528;
  --bg-input: #2A2A2E;

  --border-primary: #3A3A3C; /* Darker borders */
  --border-strong: #505054;
  --border-focus-ring: rgba(0, 224, 144, 0.3); /* Brighter Green focus */

  --button-primary-bg: var(--primary-green);
  --button-primary-text: var(--text-color-on-primary-bg);
  --button-primary-hover-bg: var(--primary-green-darker);

  --button-secondary-bg: var(--bg-content-card);
  --button-secondary-text: var(--primary-green);
  --button-secondary-border: var(--primary-green);
  --button-secondary-hover-bg: rgba(0, 224, 144, 0.1);

  --button-danger-bg: #B72D23;
  --button-danger-text: #FFFFFF;
  --button-danger-hover-bg: #9C261D;

  --message-success-bg: var(--primary-green-lighter-bg);
  --message-success-text: #90ffc7; /* Lighter green text */
  --message-success-border: #008a5e;
  --message-success-accent: var(--primary-green);

  --message-warning-bg: #4d3800; /* Dark yellow/orange */
  --message-warning-text: #FFD580;
  --message-warning-border: #856404;

  --message-error-bg: #5a1a1f; /* Dark red */
  --message-error-text: #ffb3b8;
  --message-error-border: #721C24;

  --message-info-bg: var(--primary-green-lighter-bg);
  --message-info-text: var(--primary-green);
  --message-info-border: var(--primary-green-darker);

  --link-color: var(--primary-green);
  --link-hover-color: #00f0a0;

  --shadow-card: 0 1px 3px rgba(0,0,0,0.2); /* Shadows are trickier on dark, often more subtle or inverted */
  --shadow-button: 0 2px 4px rgba(0,0,0,0.15);
  --shadow-button-hover: 0 4px 8px rgba(0,0,0,0.2);

  --checkbox-accent: var(--primary-green);
  --spinner-color: var(--primary-green);
}


body {
  font-family: 'Inter', 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  background-color: var(--bg-page);
  color: var(--text-color-primary);
  line-height: 1.65;
  transition: background-color 0.3s ease, color 0.3s ease; /* Smooth theme transition */
}

/* === App Container & Layout === */
.App-container { /* Was .App previously, ensuring it's distinct */
  max-width: 1300px;
  margin: 0 auto;
  padding: 25px;
}

header {
  background-color: var(--bg-header-footer);
  color: var(--text-color-on-dark-bg); /* Text that works on dark header */
  padding: 20px 30px;
  margin-bottom: 35px;
  border-radius: 8px;
  display: flex; /* Ensure header content aligns */
  justify-content: space-between; /* Push toggle to the right */
  align-items: center;
}

.header-content {
  display: flex; /* This is now the parent of .header-left and .theme-toggle-btn */
  align-items: center;
  flex-grow: 1; /* Allow it to take space */
}
.header-left { /* New wrapper for logo and title */
    display: flex;
    align-items: center;
    gap: 20px;
}

.header-logo {
  height: 40px;
  width: auto;
}

header h1 {
  margin: 0;
  font-size: 1.7rem;
  font-weight: 600;
  letter-spacing: -0.5px;
  color: var(--text-color-on-dark-bg); /* Ensure h1 color uses var */
}

.theme-toggle-btn {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: 1.5px solid var(--button-secondary-border);
  padding: 8px 16px;
  font-size: 0.85rem;
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
  white-space: nowrap; /* Prevent text wrapping */
  margin-left: 20px;
}
.theme-toggle-btn:hover {
  background-color: var(--button-secondary-hover-bg);
}


footer {
  text-align: center;
  padding: 25px;
  margin-top: 45px;
  color: var(--text-color-secondary);
  font-size: 0.9rem;
  border-top: 1px solid var(--border-primary);
}

hr {
  border: none;
  border-top: 1px solid var(--border-primary);
  margin: 45px 0;
}

/* === Section Styling (Cards) === */
.dashboard-section {
  background-color: var(--bg-content-card);
  border-radius: 8px;
  border: 1px solid var(--border-primary);
  padding: 30px 35px;
  margin-bottom: 35px;
  box-shadow: var(--shadow-card);
}

.dashboard-section h2 {
  font-size: 1.6rem;
  color: var(--primary-green);
  margin-top: 0;
  margin-bottom: 30px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--primary-green);
  font-weight: 600;
}

.dashboard-section h3 {
    font-size: 1.25rem;
    color: var(--text-color-primary);
    margin-top: 25px;
    margin-bottom: 18px;
    font-weight: 600;
}
.dashboard-section h4 {
    font-size: 1.1rem;
    color: var(--text-color-secondary);
    margin-top: 20px;
    margin-bottom: 12px;
    font-weight: 600;
}


/* === Form Elements === */
.form-group {
  margin-bottom: 22px;
}

.form-group label {
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
  color: var(--text-color-primary);
  font-size: 0.9rem;
}

.form-group input[type="text"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-strong); /* Using stronger border for inputs */
  border-radius: 6px;
  font-size: 0.95rem;
  color: var(--text-color-primary);
  background-color: var(--bg-input);
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.form-group input[type="text"]::placeholder,
.form-group textarea::placeholder {
    color: var(--text-color-secondary);
    opacity: 0.8;
}

.form-group input[type="text"]:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary-green);
  outline: 0;
  box-shadow: 0 0 0 0.15rem var(--border-focus-ring);
}

.form-group textarea {
  min-height: 110px;
  resize: vertical;
}

/* Input error styling */
.form-group input.input-error {
  border-color: var(--message-error-border);
  background-color: var(--message-error-bg);
}

.form-group input.input-error:focus {
  border-color: var(--message-error-border);
  box-shadow: 0 0 0 0.15rem rgba(114, 28, 36, 0.25);
}

/* === Buttons === */
button {
  padding: 12px 22px;
  /* border: 1px solid transparent; */ /* Already defined by specific button styles */
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 600;
  transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out, color 0.2s ease-in-out, transform 0.1s ease, box-shadow 0.2s ease;
  margin-right: 12px;
  margin-top: 8px;
  letter-spacing: 0.2px;
  box-shadow: var(--shadow-button);
}

button:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-button-hover);
}
button:active {
  transform: translateY(0px);
  box-shadow: var(--shadow-button);
}

/* Primary Button Style */
button:not(.remove-btn):not(.secondary-btn):not(.theme-toggle-btn) {
  background-color: var(--button-primary-bg);
  color: var(--button-primary-text);
  border: 1.5px solid var(--button-primary-bg); /* Ensure border matches bg */
}
button:not(.remove-btn):not(.secondary-btn):not(.theme-toggle-btn):hover {
  background-color: var(--button-primary-hover-bg);
  border-color: var(--button-primary-hover-bg);
}

/* Secondary Button Style */
button.secondary-btn {
  background-color: var(--button-secondary-bg);
  color: var(--button-secondary-text);
  border: 1.5px solid var(--button-secondary-border);
}
button.secondary-btn:hover {
  background-color: var(--button-secondary-hover-bg);
}

/* Remove Button Style */
.metric-item button.remove-btn {
  background-color: var(--button-danger-bg);
  color: var(--button-danger-text);
  border: 1.5px solid var(--button-danger-bg);
  font-size: 0.8rem;
  padding: 7px 14px;
}
.metric-item button.remove-btn:hover {
  background-color: var(--button-danger-hover-bg);
  border-color: var(--button-danger-hover-bg);
}

button:disabled {
  background-color: var(--text-color-secondary); /* Using secondary text for disabled bg */
  color: var(--bg-page); /* Opposite for text */
  border-color: var(--text-color-secondary);
  cursor: not-allowed;
  transform: none;
  opacity: 0.5; /* More pronounced disabled state */
  box-shadow: none;
}

/* === Metric List Display === */
.metric-item {
  background-color: var(--bg-metric-item);
  padding: 14px 20px;
  border-radius: 6px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid var(--border-primary);
}

.metric-item span {
  font-size: 0.95rem;
  color: var(--text-color-primary);
}
.metric-item span strong {
    color: var(--text-color-primary);
    font-weight: 600;
}
.metric-item span code {
    background-color: var(--border-primary); /* Slightly different bg for code in metric item */
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.85em;
    color: var(--text-color-secondary);
}


/* === Messages (Alerts/Info) === */
.message {
  padding: 14px 18px;
  margin: 18px 0;
  border-radius: 6px;
  font-weight: 500;
  border-width: 1px;
  border-style: solid;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.message strong {
    font-weight: 700;
    margin-right: 8px;
}

.message-ok {
  background-color: var(--message-success-bg);
  border-color: var(--message-success-border);
  color: var(--message-success-text);
}

.message-warning {
  background-color: var(--message-warning-bg);
  border-color: var(--message-warning-border);
  color: var(--message-warning-text);
}

.message-fatal {
  background-color: var(--message-error-bg);
  border-color: var(--message-error-border);
  color: var(--message-error-text);
}

.message-info {
  background-color: var(--message-info-bg);
  border-color: var(--message-info-border);
  color: var(--message-info-text);
}

/* === Label Selection === */
.label-selection-container {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid var(--border-primary);
}
.label-selection-container:first-child {
    border-top: none;
    padding-top: 0;
}

.label-group {
  margin-bottom: 12px;
}

.label-group strong {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--text-color-primary);
  font-size: 1rem;
}

.label-group label {
  margin-right: 22px;
  font-weight: 400;
  display: inline-flex;
  align-items: center;
  font-size: 0.9rem;
  cursor: pointer;
  color: var(--text-color-secondary);
}
.label-group input[type="checkbox"] {
    margin-right: 10px;
    width: auto;
    height: auto;
    transform: scale(1.15);
    accent-color: var(--checkbox-accent);
    border: 1px solid var(--border-strong); /* ensure checkbox border is visible in dark mode */
}
body.dark-theme .label-group input[type="checkbox"] {
    background-color: var(--bg-input); /* Helps with visibility of unchecked box in dark mode */
}

/* === Recording Rule Selection === */
.recording-rule-selection-container {
  margin-top: 25px;
  padding-top: 20px;
  border-top: 1px solid var(--border-primary);
}
.recording-rule-selection-container:first-child {
    border-top: none;
    padding-top: 0;
}

.metric-header {
  margin-bottom: 15px;
}

.recording-rule-checkbox {
  margin: 15px 0;
}

.recording-rule-checkbox label {
  display: flex;
  align-items: center;
  font-size: 0.95rem;
  cursor: pointer;
  color: var(--text-color-primary);
  font-weight: 500;
}

.recording-rule-checkbox input[type="checkbox"] {
  margin-right: 10px;
  width: auto;
  height: auto;
  transform: scale(1.2);
  accent-color: var(--checkbox-accent);
  border: 1px solid var(--border-strong);
}

body.dark-theme .recording-rule-checkbox input[type="checkbox"] {
    background-color: var(--bg-input);
}

.recording-rule-checkbox input[type="checkbox"]:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.recording-rule-checkbox label:has(input:disabled) {
  cursor: not-allowed;
}

.recording-rule-recommendation {
  margin-top: 15px;
  padding: 15px;
  background-color: var(--bg-metric-item);
  border-radius: 6px;
  border: 1px solid var(--border-primary);
}

.recommendation-content h5 {
  margin-top: 0;
  margin-bottom: 10px;
  color: var(--text-color-primary);
}

.rule-example {
  background-color: var(--bg-code-block);
  color: var(--text-color-on-dark-bg);
  padding: 15px;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 0.85em;
  white-space: pre-wrap;
  word-break: break-all;
  border: 1px solid var(--border-primary);
  margin-top: 10px;
}

.selected-labels-info {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid var(--border-primary);
  color: var(--text-color-secondary);
}

/* === Recommendation Engine & Jira (Code Blocks) === */
.recommendation, .jira-summary-container {
  margin-top: 25px;
}

.recommendation pre, .jira-summary-container pre {
  background-color: var(--bg-code-block);
  color: var(--text-color-on-dark-bg); /* Use text for dark bg for code */
  padding: 20px;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 0.88em;
  white-space: pre-wrap;
  word-break: break-all;
  border: 1px solid var(--border-primary);
}

/* === Loading Spinner === */
.loading-spinner {
  border: 5px solid var(--border-strong); /* Use stronger border for base */
  border-top: 5px solid var(--spinner-color);
  border-radius: 50%;
  width: 38px;
  height: 38px;
  animation: spin 0.7s linear infinite;
  margin: 30px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* === Jira Success Message === */
.jira-success-message {
  background-color: var(--message-success-bg);
  color: var(--message-success-text);
  padding: 18px 22px;
  border: 1px solid var(--message-success-border);
  border-left-width: 5px;
  border-left-color: var(--message-success-accent);
  border-radius: 6px;
  margin-top: 25px;
}

.jira-success-message p {
  margin: 6px 0;
}

.jira-success-message strong {
  color: var(--message-success-text); /* Make strong use same color for simplicity or a slightly darker shade if defined */
  font-weight: 700;
}

.jira-success-message a {
  color: var(--link-color);
  font-weight: 600;
  text-decoration: none;
}
.jira-success-message a:hover {
  text-decoration: underline;
  color: var(--link-hover-color);
}
.editable-rule-yaml, /* Add .editable-rule-yaml to share styles */
.editable-alert-yaml {
  width: 100%;
  padding: 15px;
  border: 1px solid var(--border-strong);
  border-radius: 6px;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 0.9em;
  line-height: 1.5;
  background-color: var(--bg-code-block);
  color: var(--text-color-on-dark-bg);
  resize: vertical;
  min-height: 150px; /* You can adjust min-height for rules vs alerts if desired */
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
  margin-top: 10px; /* Add some space above the textarea */
}

body.light-theme .editable-rule-yaml, /* Specific light theme overrides if needed */
body.light-theme .editable-alert-yaml {
  background-color: #2b303b;
  color: #c0c5ce;
  border-color: #3e4451;
}

body.dark-theme .editable-rule-yaml, /* Specific dark theme overrides if needed */
body.dark-theme .editable-alert-yaml {
  background-color: var(--bg-code-block);
  color: var(--text-color-on-dark-bg);
  border-color: var(--border-strong);
}

.editable-rule-yaml:focus,
.editable-alert-yaml:focus {
  border-color: var(--primary-green);
  outline: 0;
  box-shadow: 0 0 0 0.15rem var(--border-focus-ring), inset 0 1px 3px rgba(0,0,0,0.1);
}