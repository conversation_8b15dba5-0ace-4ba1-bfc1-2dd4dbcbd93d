# Metrics Onboarding & Management Portal

A comprehensive self-service platform for metrics whitelisting and management, designed for Palo Alto Networks Cortex SRE and Development teams. This application provides an intuitive web interface for managing metrics, performing cardinality checks, and automating Jira ticket creation.

## 🏗️ Architecture Overview

The application follows a modern three-tier architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  Flask Backend  │    │  External APIs  │
│                 │    │                 │    │                 │
│  • Dashboard    │◄──►│  • REST API     │◄──►│  • Jira API     │
│  • Components   │    │  • Data Processing │  │  • Metrics APIs │
│  • State Mgmt   │    │  • Jira Integration│  │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 Repository Structure

```
whitelisting-self-service/
├── react-frontend/          # React.js web application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── utils/          # API utilities and mock data
│   │   ├── App.js          # Main application component
│   │   └── Dashboard.js    # Main dashboard interface
│   ├── public/             # Static assets
│   ├── Dockerfile          # Frontend containerization
│   └── package.json        # Node.js dependencies
│
├── flask-backend/          # Python Flask API server
│   ├── flask_startup.py   # Main Flask application
│   ├── flask_csv_tools.py # CSV processing utilities
│   ├── jira_get_meta.py   # Jira metadata handling
│   ├── unit-tests/        # Backend unit tests
│   ├── Dockerfile         # Backend containerization
│   └── requirements.txt   # Python dependencies
│
├── .gitlab-ci.yml         # CI/CD pipeline configuration
├── bootstrap.py           # Local development setup script
└── README.md             # This documentation
```

## 🚀 Features

### Frontend (React Application)
- **🎨 Modern UI**: Clean, responsive interface with light/dark theme support
- **📊 Dashboard**: Centralized metrics management interface
- **🔧 Component-Based**: Modular React components for maintainability
- **📱 Responsive Design**: Works on desktop and mobile devices
- **⚡ Real-time Updates**: Dynamic state management and API integration

### Backend (Flask API)
- **🔌 RESTful API**: Clean API endpoints for frontend integration
- **📈 Metrics Processing**: Handle metrics data and cardinality checks
- **🎫 Jira Integration**: Automated ticket creation and management
- **🔒 CORS Support**: Secure cross-origin resource sharing
- **📊 Data Validation**: Input validation and error handling

### Key Functionality
- **📋 Metrics Management**: Add, remove, and organize metrics
- **🔍 Cardinality Checking**: Validate metric cardinality and emission
- **🏷️ Label Selection**: Dynamic label management for metrics
- **📝 Recording Rules**: Generate Prometheus recording rule examples
- **🚨 Alert Generation**: Create alert rule examples
- **🎫 Jira Automation**: Automated ticket creation with metadata
- **🌿 Branch Management**: Source and target branch selection
- **🏢 Tenant Management**: QA tenant and LCaaS ID handling

## 🛠️ Technology Stack

### Frontend
- **React 19.1.0**: Modern React with hooks and functional components
- **JavaScript ES6+**: Modern JavaScript features
- **CSS3**: Custom styling with theme support
- **Axios**: HTTP client for API communication
- **React Testing Library**: Unit testing framework

### Backend
- **Python 3.10**: Modern Python with type hints
- **Flask**: Lightweight web framework
- **Flask-CORS**: Cross-origin resource sharing
- **Atlassian Python API**: Jira integration
- **Requests**: HTTP library for external API calls

### DevOps & Infrastructure
- **Docker**: Containerization for both frontend and backend
- **GitLab CI/CD**: Automated testing and deployment
- **Kaniko**: Container image building in CI/CD
- **Google Cloud**: Container registry and deployment

## 🚀 Quick Start

### Prerequisites
- **Node.js 18+** (for frontend development)
- **Python 3.10+** (for backend development)
- **Docker** (for containerized deployment)
- **Git** (for version control)

### Local Development

#### 1. Clone the Repository
```bash
git clone <repository-url>
cd whitelisting-self-service
```

#### 2. Frontend Setup
```bash
cd react-frontend
npm install
npm start
# Frontend will be available at http://localhost:3000
```

#### 3. Backend Setup
```bash
cd flask-backend
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python flask_startup.py
# Backend will be available at http://localhost:5000
```

#### 4. Using Bootstrap Script
```bash
# Automated setup using the provided bootstrap script
python bootstrap.py
```

### Docker Deployment

#### Frontend Container
```bash
cd react-frontend
docker build -t metrics-frontend .
docker run -p 80:80 metrics-frontend
```

#### Backend Container
```bash
cd flask-backend
docker build -t metrics-backend .
docker run -p 5000:5000 metrics-backend
```

## 🧪 Testing

### Frontend Tests
```bash
cd react-frontend
npm test                    # Run tests in watch mode
npm test -- --watchAll=false  # Run tests once
npm test -- --coverage     # Run with coverage report
```

### Backend Tests
```bash
cd flask-backend/unit-tests
pip install -r test_requirements.txt
python test_summary.py
```

### CI/CD Testing
The GitLab CI pipeline automatically runs:
- **Frontend Unit Tests**: React component and utility tests
- **Backend Unit Tests**: Python Flask API tests
- **Build Validation**: Docker container builds

## 🔧 Configuration

### Environment Variables

#### Backend Configuration
```bash
export JIRA_URL="https://jira-dc.paloaltonetworks.com/"
export JIRA_TOKEN="your-jira-api-token"
```

#### Frontend Configuration
- API endpoints are configured in `src/utils/mockApi.js`
- Mock data is available in `src/utils/mockData.js`

### Jira Integration
- **Project Key**: CRTX
- **Issue Type**: Task
- **Custom Fields**: Configured for metrics metadata

## 📊 API Endpoints

### Backend REST API

#### POST `/process_data`
Process metrics data and perform cardinality checks
```json
{
  "metrics": [{"id": "1", "name": "metric_name", "pod": "pod_name"}],
  "qaTenant": "qa2-test",
  "qaTenantLcaasId": "12345"
}
```

#### POST `/create_jira`
Create Jira tickets with metrics metadata
```json
{
  "summary": "Metrics Whitelisting Request",
  "description": "Request details...",
  "metrics": [...],
  "cardinality": {...}
}
```

## 🚀 Deployment

### GitLab CI/CD Pipeline

The repository includes a comprehensive CI/CD pipeline:

```yaml
Stages:
  1. unit-test:
     - Frontend-Unit-Tests (Node.js)
     - Backend-Unit-Tests (Python)

  2. build:
     - build-frontend (Docker)
     - build-backend (Docker)
```

### Container Registry
- **Frontend**: `us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/buffet-frontend`
- **Backend**: `us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/buffet-backend`

## 🤝 Contributing

### Development Workflow
1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Commit** your changes (`git commit -m 'Add amazing feature'`)
4. **Push** to the branch (`git push origin feature/amazing-feature`)
5. **Open** a Pull Request

### Code Standards
- **Frontend**: ESLint configuration with React best practices
- **Backend**: PEP 8 Python style guide
- **Testing**: Maintain test coverage above 70%
- **Documentation**: Update README for significant changes

## 📝 License

This project is proprietary software developed for Palo Alto Networks Cortex SRE teams.

## 👥 Team

**Palo Alto Networks Cortex SRE Team**
- Metrics Management and Whitelisting
- Infrastructure Automation
- Developer Experience

## 🆘 Support

For support and questions:
- **Internal Teams**: Contact the Cortex SRE team
- **Issues**: Use GitLab issue tracker
- **Documentation**: Refer to this README and inline code comments

---

**✨ Built with ❤️ by the Palo Alto Networks Cortex SRE Team**
<!---Protected_by_PANW_Code_Armor_2024 - eGRyfC94ZHIvZGV2b3BzL3doaXRlbGlzdGluZy1zZWxmLXNlcnZpY2V8MzE0MHxtYXN0ZXI= --->
