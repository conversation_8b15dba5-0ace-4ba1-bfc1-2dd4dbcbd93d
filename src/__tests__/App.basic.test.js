import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import App from '../App';

// Mock Dashboard component to isolate App component testing
jest.mock('../Dashboard', () => {
  return function MockDashboard() {
    return <div data-testid="dashboard">Dashboard Component</div>;
  };
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

describe('App Component - Basic Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue('light');
    document.body.className = '';
  });

  test('renders without crashing', () => {
    render(<App />);
    expect(screen.getByTestId('dashboard')).toBeInTheDocument();
  });

  test('renders header with title', () => {
    render(<App />);
    expect(screen.getByText('Metrics Onboarding & Management Portal')).toBeInTheDocument();
  });

  test('renders logo', () => {
    render(<App />);
    expect(screen.getByAltText('Palo Alto Networks Logo')).toBeInTheDocument();
  });

  test('renders theme toggle button', () => {
    render(<App />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });

  test('renders footer', () => {
    render(<App />);
    expect(screen.getByText(/Your Palo Alto Networks Cortex SRE Team/)).toBeInTheDocument();
  });
});
