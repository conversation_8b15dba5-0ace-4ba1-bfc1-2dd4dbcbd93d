import React, { useEffect } from 'react';

function LabelSelection({ metrics, selectedLabels, onLabelsChange, getAvailableLabelsForMetric, cardinalityResults }) {
  // Auto-select all default labels for new metrics
  useEffect(() => {
    metrics.forEach(metric => {
      const currentLabels = selectedLabels[metric.id] || [];
      const availableLabels = getAvailableLabelsForMetric(metric.name);

      // If no labels are selected for this metric, auto-select all available labels
      if (currentLabels.length === 0 && availableLabels.length > 0) {
        onLabelsChange(metric.id, availableLabels);
      }
    });
  }, [metrics, selectedLabels, onLabelsChange, getAvailableLabelsForMetric]);

  // Component is completely hidden but functionality remains active
  // Auto-selection of labels happens in useEffect above
  return null;
}

export default LabelSelection;