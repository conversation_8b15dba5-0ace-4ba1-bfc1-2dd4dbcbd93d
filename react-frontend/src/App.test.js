// This file has been moved to src/__tests__/App.test.js
// Please run tests from the __tests__ directory for better organization

import { render, screen } from '@testing-library/react';
import App from './App';

// Mock Dashboard component to avoid complex dependencies
jest.mock('./Dashboard', () => {
  return function MockDashboard() {
    return <div data-testid="dashboard">Dashboard Component</div>;
  };
});

test('renders app component', () => {
  render(<App />);
  const headerElement = screen.getByText(/Metrics Onboarding & Management Portal/i);
  expect(headerElement).toBeInTheDocument();
});
