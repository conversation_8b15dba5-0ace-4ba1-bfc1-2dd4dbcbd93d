import React, { useState, useMemo } from 'react';

function SlackBotLinkInput({ onAddLink, onBatchAddLinks }) {
  const [slacklink, setSlackLink] = useState('');
  const [batchInput, setBatchInput] = useState('');
  const [duplicateMessage, setDuplicateMessage] = useState('');

  const hasSpacesOrCommas = (s) => /\s|,/.test(s);
  const isValidUrl = (s) => {
    try {
      const u = new URL(s);
      return (u.protocol === 'http:' || u.protocol === 'https:');
    } catch {
      return false;
    }
  };

  const singleError = useMemo(() => {
    const v = slacklink.trim();
    if (!v) return '';
    if (hasSpacesOrCommas(v)) return 'No spaces or commas allowed.';
    if (!isValidUrl(v)) return 'Must be a valid http/https URL.';
    return '';
  }, [slacklink]);

  const batchAnalysis = useMemo(() => {
    const lines = batchInput.split('\n');
    const cleaned = [];
    const errors = [];
    lines.forEach((raw, idx) => {
      const v = raw.trim();
      if (!v) return; // allow empty lines
      if (hasSpacesOrCommas(v)) {
        errors.push({ index: idx + 1, value: v, reason: 'contains spaces or commas' });
      } else if (!isValidUrl(v)) {
        errors.push({ index: idx + 1, value: v, reason: 'invalid URL' });
      } else {
        cleaned.push(v);
      }
    });
    return { cleaned, errors };
  }, [batchInput]);

  const handleAdd = () => {
    const v = slacklink.trim();
    if (!v || singleError) return;
    setDuplicateMessage(''); // Clear any previous duplicate message
    onAddLink(v, (duplicateLink) => {
      setDuplicateMessage(`Slack link duplicate link was already added to the list .`);
    });
    setSlackLink('');
  };

  const handleBatch = () => {
    const { errors } = batchAnalysis;
    if (batchInput.trim() === '' || errors.length > 0) return;
    setDuplicateMessage(''); // Clear any previous duplicate message
    if (onBatchAddLinks) {
      onBatchAddLinks(batchInput, (duplicates) => {
        if (duplicates.length === 1) {
          setDuplicateMessage(`1 Slack link duplicate, the link was already added to the list.`);
        } else if (duplicates.length > 1) {
          setDuplicateMessage(`${duplicates.length} Slack links already exist and were not added.`);
        }
      });
    }
    setBatchInput('');
  };

  return (
    <section className="dashboard-section">
      <h2>4. Cardinality Evaluation Slack Bot Links</h2>
      <div className="form-group">
        <label htmlFor="slacklink">Slack Bot Link:</label>
        <input
          type="text"
          id="slacklink"
          value={slacklink}
          onChange={(e) => {
            setSlackLink(e.target.value);
            setDuplicateMessage(''); // Clear duplicate message when user starts typing
          }}
          placeholder="https://panw-rnd.slack.com/archives/CHANNEL_ID/p1234567890123456"
        />
        {singleError && (
          <p className="message message-warning" style={{ marginTop: '6px' }}>{singleError}</p>
        )}
        {duplicateMessage && (
          <p className="message message-warning" style={{ marginTop: '6px' }}>{duplicateMessage}</p>
        )}
      </div>
      <button onClick={handleAdd} disabled={!slacklink.trim() || !!singleError}>Add Slack link individually</button>

      <div className="form-group" style={{ marginTop: '20px' }}>
        <label htmlFor="slackBatch">Batch Add Slack Links (one URL per line, no spaces/commas):</label>
        <textarea
          id="slackBatch"
          value={batchInput}
          onChange={(e) => {
            setBatchInput(e.target.value);
            setDuplicateMessage(''); // Clear duplicate message when user starts typing
          }}
          placeholder={`https://panw-rnd.slack.com/archives/CXXXX/p1111111111111111\nhttps://panw-rnd.slack.com/archives/CYYYY/p2222222222222222`}
        />
      </div>
      {batchAnalysis.errors.length > 0 && (
        <div className="message message-warning" style={{ marginBottom: '8px' }}>
          <strong>Invalid lines:</strong>
          <ul style={{ marginTop: '6px' }}>
            {batchAnalysis.errors.slice(0, 5).map((e) => (
              <li key={`${e.index}-${e.value}`}>Line {e.index}: {e.reason}</li>
            ))}
            {batchAnalysis.errors.length > 5 && (
              <li>...and {batchAnalysis.errors.length - 5} more</li>
            )}
          </ul>
        </div>
      )}
      <button onClick={handleBatch} disabled={batchInput.trim() === '' || batchAnalysis.errors.length > 0}>Add Batch</button>
    </section>
  );
}

export default SlackBotLinkInput;