stages:
  - unit-test
  - build

variables:
  DOCKER_REPO: whitelisting-self-service
  DOCKER_TAG: $CI_PIPELINE_ID
  MT_BRANCH: master
  PROJECT: kuna-224012
  LOCATION: europe-west4
  PIP_CACHE_DIR: "$CI_PROJECT_DIR/.cache/pip"
  REPOSITORY: xdr

Backend-Unit-Tests:
  image: python:3.10
  stage: unit-test
  allow_failure: false
  script:
    - pip install -r flask-backend/unit-tests/test_requirements.txt
    - pip install -r flask-backend/requirements.txt
    - cd flask-backend/unit-tests
    - python3 test_summary.py

Frontend-Unit-Tests:
  image: node:18-alpine
  stage: unit-test
  allow_failure: false
  cache:
    paths:
      - react-frontend/node_modules/
  script:
    - cd react-frontend
    - npm ci
    - npm test -- --watchAll=false --coverage=false


build-backend:
  image:
    name: gcr.io/kaniko-project/executor:v1.6.0-debug
    entrypoint: [ "" ]
  stage: build
  script:
    - set -x
    - env
    - mkdir -p /kaniko/.docker
    - >
      /kaniko/executor
      --cache=true
      --cache-repo=${DOCKER_REPO}/cache
      --context $CI_PROJECT_DIR/flask-backend
      --dockerfile $CI_PROJECT_DIR/flask-backend/Dockerfile
      --destination  us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/buffet-backend:$CI_PIPELINE_ID
      --destination  us-docker.pkg.dev/xdr-registry-dev-01/devops-tools/buffet-backend:$CI_PIPELINE_ID
  # rules:
  #       - if: '$CI_COMMIT_BRANCH == "master"'


build-frontend:
  image:
    name: gcr.io/kaniko-project/executor:v1.6.0-debug
    entrypoint: [ "" ]
  stage: build
  script:
    - set -x
    - env
    - mkdir -p /kaniko/.docker
    - >
      /kaniko/executor
      --cache=true
      --cache-repo=${DOCKER_REPO}/cache
      --context $CI_PROJECT_DIR/react-frontend
      --dockerfile $CI_PROJECT_DIR/react-frontend/Dockerfile
      --destination  us-docker.pkg.dev/xdr-registry-dev-01/cortex-xdr/buffet-frontend:$CI_PIPELINE_ID
      --destination  us-docker.pkg.dev/xdr-registry-dev-01/devops-tools/buffet-frontend:$CI_PIPELINE_ID
  # rules:
  #       - if: '$CI_COMMIT_BRANCH == "master"'

