import React from 'react';
//due to slight oversight qaTenant == environment variable - 
function <PERSON><PERSON><PERSON>hecker({ onRunChecks, results, metrics, isLoading, qaTenant , qaTenantLcaasId }) {
  return (
    <section className="dashboard-section">
      <h2>3. Run Cardinality & Emission Check</h2>
      <button onClick={onRunChecks} disabled={isLoading || metrics.length === 0 || !qaTenant}>
        {isLoading ? 'Checking...' : `Run Checks on ${qaTenant+'-'+qaTenantLcaasId || 'Selected QA Tenant'}`}
      </button>
      {isLoading && <div className="loading-spinner"></div>}
      {!isLoading && Object.keys(results).length > 0 && (
        <div style={{ marginTop: '15px' }}>
          <h3>Check Results:</h3>
          {metrics.map(metric => {
            const result = results[metric.id];
            if (!result) return <div key={metric.id} className="message message-info">Metric: {metric.name} - Pending check.</div>;
            return (
              <div key={metric.id} className={`message message-${result.status?.toLowerCase()}`}>
                <strong>{metric.name}{metric.pod ? ` (Pod: ${metric.pod})` : ''}:</strong> {result.status} - {result.message}
                {result.isOmitted && <span> (Omitted on {result.checkedTenant})</span>}
              </div>
            );
          })}
        </div>
      )}
       {!isLoading && metrics.length > 0 && Object.keys(results).length === 0 && qaTenant && (
        <p className="message message-info" style={{marginTop: '10px'}}>Ready to run checks on {qaTenant}.</p>
       )}
    </section>
  );
}

export default CardinalityChecker;