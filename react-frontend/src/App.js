import React, { useState, useEffect, useCallback } from 'react';
import Dashboard from './Dashboard';
import './App.css';

const paloAltoNetworksLogoUrl = 'https://upload.wikimedia.org/wikipedia/commons/d/de/PaloAltoNetworks_2020_Logo.svg';

function App() {
  // Theme state: 'light' or 'dark'
  const [theme, setTheme] = useState(() => {
    const savedTheme = localStorage.getItem('app-theme');
    return savedTheme || 'light'; // Default to light theme
  });

  // Effect to apply the theme class to the body and save to localStorage
  useEffect(() => {
    document.body.className = ''; // Clear existing theme classes
    document.body.classList.add(`${theme}-theme`);
    localStorage.setItem('app-theme', theme);
  }, [theme]);

  const toggleTheme = useCallback(() => {
    setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  }, []);

  return (
    <div className={`App-container ${theme}-theme`}> {/* Apply theme class here too for specific app container styling if needed */}
      <header>
        <div className="header-content">
          <div className="header-left"> {/* Wrapper for logo and title */}
            <img
              src={paloAltoNetworksLogoUrl}
              alt="Palo Alto Networks Logo"
              className="header-logo"
            />
            <h1>Metrics Onboarding & Management Portal</h1>
          </div>
          <button onClick={toggleTheme} className="theme-toggle-btn">
            Switch to {theme === 'light' ? 'Dark' : 'Light'} Mode
          </button>
        </div>
      </header>
      <main>
        <Dashboard />
      </main>
      <footer>
        <p>&copy; {new Date().getFullYear()}  Your Palo Alto Networks Cortex SRE Team </p>
      </footer>
    </div>
  );
}

export default App;