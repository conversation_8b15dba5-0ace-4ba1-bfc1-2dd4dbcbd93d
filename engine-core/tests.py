import verifier_tools
from verifier_tools import  write_csv_db , clean_csv_file_prepare_for_mysql , mysql_run_query, deduplicate_csv
import pickle
import mysql.connector 
metric = 'agent_to_group_calculation_count'
import pandas as pd

def main():
    
    clean_csv_file_prepare_for_mysql('/Users/<USER>/Documents/Repos/prometheus-config/tools/whitelist-verifier/db_ready_for_import.csv')

# Example usage:





if __name__ == "__main__":
    main()
