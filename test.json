{
  "summary": "Metrics Onboarding Request for  ST MAIN qa2-test-9137138)",
  "description": "\n{\"target_version\":\"Next Version (v3.16)\",\"environment\": \"ST MAIN\",\"project_id\": \"qa2-test-9137138\"}\n\n\"Metric\": \"akdjakdj\"recording_rule_for : akdjakdj\n  - record: tenant:akdjakdj:sum\n    expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id) (akdjakdj)\n",
  "lcaas": "qa2-test-9137138",
  "metrics": [
    {
      "id": "metric-0",
      "name": "akdjakdj",
      "pod": ""
    }
  ],
  "rules": "\nrecording_rule_for : akdjakdj\n  - record: tenant:akdjakdj:sum\n    expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id) (akdjakdj)",
  "slackLinks": []