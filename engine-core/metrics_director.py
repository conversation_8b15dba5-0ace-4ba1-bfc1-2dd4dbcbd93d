from metrics_builders import MetricsBuilder
from verifier_tools import enumerate_whitelist_entries , clean_csv_file_prepare_for_mysql , deduplicate_csv
from alive_progress import alive_bar
import time

def create_metric_object(name):
    metric = (MetricsBuilder()
              .add_metric_name(name)
              .add_rr(name)
              .add_blacklist()
              .build()
              
    )
    metric.create()



def main():
    whitelist = enumerate_whitelist_entries()
    entries = []
    with alive_bar(len(whitelist)) as bar:
        for entry in whitelist:
            # create_metric_object(entry)
            object_entry = create_metric_object(entry)
            entries.append(object_entry)
            bar()
        # clean_csv_file_prepare_for_mysql('engine-core/data_dict.csv')
        deduplicate_csv('engine-core/data_dict.csv')
    
        

if __name__ == "__main__":
    main()


