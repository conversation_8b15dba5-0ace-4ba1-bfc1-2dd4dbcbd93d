import React from 'react';

function SlackLinkListDisplay({ links, onRemoveLink }) {
  if (!links || links.length === 0) {
    return (
      <section className="dashboard-section">
        <h3>Added Slack Bot Links:</h3>
        <p>No Slack links added yet.</p>
      </section>
    );
  }

  return (
    <section className="dashboard-section">
      <h3>Added Slack Bot Links:</h3>
      {links.map((link, index) => (
        <div key={`${link}-${index}`} className="metric-item">
          <span>
            <a href={link} target="_blank" rel="noopener noreferrer">{link}</a>
          </span>
          <button onClick={() => onRemoveLink(index)} className="remove-btn">Remove</button>
        </div>
      ))}
    </section>
  );
}

export default SlackLinkListDisplay;

