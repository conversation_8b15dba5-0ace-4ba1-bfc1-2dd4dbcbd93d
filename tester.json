{"summary": "Metrics Onboarding Request for  (ENV: ST MAIN-qa2-test-1231223)", "description": "\\n{target_branch: Next Version (v3.16)}\\n{Environment : ST MAIN}\\n{project_id: qa2-test-1231223}\\n{metrics_table: | metetete | N/A | N/A | No | product_tier, product_type, tenant_type, kubernetes_namespace, xdr_id, namespace, lcaas_id | Yes (see example) |}\\n\\nh3. Requested Rules and Alert review\\n\\nh4. Metric: metetete\\nCardinality/Emission: Pending for selected Project.\\n* Selected Labels: product_tier, product_type, tenant_type, kubernetes_namespace, xdr_id, namespace, lcaas_id\\n* Recording Rule (Editable by Dev):\\n{code:yaml}\\n# rule_for_metric: metetete\\n\\n  - record: tenant:metetete:sum\\n    expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id) (metetete)\\n{code}\\n\\n\\n", "lcaas": "", "metrics": [{"id": "metric-0", "name": "metetete", "pod": ""}], "rules": "\\n# rule_for_metric: metetete\\n\\n  - record: tenant:metetete:sum\\n    expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id) (metetete)", "alerts": "", "cardinality": {}, "slackLinks": [], "metricsPerPod": [{"name": "metetete", "pod": "N/A", "id": "metric-0"}], "cardinalityPerPod": ""}'}, 'project': {'id': 'CRTX'}}