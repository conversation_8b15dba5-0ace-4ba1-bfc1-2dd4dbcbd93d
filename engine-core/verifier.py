# Each metric needs to have the following properties : 
# metric_name : blah,
# RR: true,
# blacklist : true
# note
from verifier_tools import open_db, merge_dicts, write_csv_db , deduplicate_csv

class MetricObject:
    def __init__(self):
        self._metric_name = ""
        self._RR = False
        self._blacklist = False
        # self._note = "" - Optional for maintenance purposes 
        # self._cardinality_max = ''
        # self._cardinality_per_pod = ''
        # 

    def __repr__(self):
        construct_1 = f"\nMetric Name: {self._metric_name}\n"
        construct_2 = f"Recording Rule : {self._RR}\n"
        construct_3 = f"Blacklist_Exists: {self._blacklist}\n"
        # construct_4 = f"Note: {self._note}\n"
        # construct_5 = f"Cardinality_Max: {self._cardinality_max}\n"
        # construct_6 = f"Cardinality_Max: {self._cardinality_per_pod}\n"
        return construct_1 + construct_2 + construct_3

    def set_metric_name(self, metric: str):
        self._metric_name = metric

    def set_rr(self, rr):
        self._RR = rr
    
    def set_blacklist(self, blacklist: bool):
        self._blacklist = blacklist

    def set_note(self, note: str):
        self._note = note

    def create(self):
        # if metric does not exist unless --recalculate flag is used write self to dictionary 
        construct = {'Metric Name': {self._metric_name}, 'Recording Rule': {self._RR}, 'Blacklist_Exists': {self._blacklist}}
        dict_of_constructs = construct
        # Reading dictionary from the binary file
        write_csv_db(data=dict_of_constructs, metric=self._metric_name)
        # write_db(dict_of_constructs)
        # new_dict_for_binary = merge_dicts(res, dict_of_constructs)  
        # writing dictionary to a binary file
        # write_db(new_dict_for_binary)
        # printing for visual confirmation