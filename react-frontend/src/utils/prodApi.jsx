import React, { useState } from 'react';
import axios from 'axios';

function cardinality_checker({ initialMetrics, initialQaTenant, initialQaTenantLcaasId }) {
  // State to hold the data that will be sent
  // You might get these values from user input, props, or other parts of your app
  const [metrics, setMetrics] = useState(initialMetrics || '');
  const [qaTenant, setQaTenant] = useState(initialQaTenant || '');
  const [qaTenantLcaasId, setQaTenantLcaasId] = useState(initialQaTenantLcaasId || '');
  const [apiResponse, setApiResponse] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Function to handle the API call
  const sendDataToFlask = async () => {
    // 1. Construct the JSON payload
    const payload = {
      metrics: metrics,
      qaTenant: qaTenant,
      qaTenantLcaasId: qaTenantLcaasId,
    };

    // Log the payload to see what you're sending (for debugging)
    console.log('Sending JSON Payload:', payload);

    setIsLoading(true);
    setApiResponse(null); // Clear previous response
    setError(null);       // Clear previous error

    try {
      // 2. Make the API call using Axios
      // Replace 'http://127.0.0.1:5000/process_data' with actual Flask API endpoint
      const response = await axios.post('http://127.0.0.1:5001/process_data', payload, {
        headers: {
          'Content-Type': 'application/json' // Axios often sets this automatically for objects, but good to be explicit
        }
      });

      // 3. Handle the successful response
      console.log('API Response:', response.data);
      setApiResponse(response.data);
    } catch (err) {
      // 4. Handle errors
      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('API Error Response:', err.response.data);
        setError(`Server Error: ${err.response.data.message || 'Unknown server error'}`);
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Network Error: No response received', err.request);
        setError('Network Error: No response from server. Is the Flask API running?');
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Axios Error:', err.message);
        setError(`Client Error: ${err.message}`);
      }
    } finally {
      setIsLoading(false); // End loading regardless of success or failure
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '8px', maxWidth: '500px', margin: '20px auto' }}>
      <h2>Send Data to Flask API</h2>

      <div style={{ marginBottom: '10px' }}>
        <label htmlFor="metricsInput" style={{ display: 'block', marginBottom: '5px' }}>Metrics:</label>
        <input
          id="metricsInput"
          type="text"
          value={metrics}
          onChange={(e) => setMetrics(e.target.value)}
          placeholder="e.g., usage, performance"
          style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }}
        />
      </div>

      <div style={{ marginBottom: '10px' }}>
        <label htmlFor="qaTenantInput" style={{ display: 'block', marginBottom: '5px' }}>QA Tenant:</label>
        <input
          id="qaTenantInput"
          type="text"
          value={qaTenant}
          onChange={(e) => setQaTenant(e.target.value)}
          placeholder="e.g., tenant_a"
          style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label htmlFor="qaTenantLcaasIdInput" style={{ display: 'block', marginBottom: '5px' }}>QA Tenant LCAAS ID:</label>
        <input
          id="qaTenantLcaasIdInput"
          type="text"
          value={qaTenantLcaasId}
          onChange={(e) => setQaTenantLcaasId(e.target.value)}
          placeholder="e.g., 12345"
          style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }}
        />
      </div>

      <button
        onClick={sendDataToFlask}
        disabled={isLoading || !metrics || !qaTenant || !qaTenantLcaasId} // Disable if loading or inputs are empty
        style={{ padding: '10px 20px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}
      >
        {isLoading ? 'Sending...' : 'Send Data to Flask'}
      </button>

      {isLoading && <p style={{ color: '#007bff' }}>Loading...</p>}
      {error && <p style={{ color: 'red' }}>Error: {error}</p>}
      {apiResponse && (
        <div style={{ marginTop: '20px', backgroundColor: '#e9f7ef', padding: '15px', borderRadius: '5px', border: '1px solid #cce8d0' }}>
          <h3>API Response:</h3>
          <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
            {JSON.stringify(apiResponse, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}

export default cardinality_checker;



//async (metrics, qaTenant, qaTenantLcaasId) =>






function jira_creator({ initialMetrics, initialQaTenant, initialQaTenantLcaasId, initialRules, initialAlerts , initialCardinality}) {
  // State to hold the data that will be sent
  // You might get these values from user input, props, or other parts of your app
  const [metrics, setMetrics] = useState(initialMetrics || '');
  const [qaTenant, setQaTenant] = useState(initialQaTenant || '');
  const [qaTenantLcaasId, setQaTenantLcaasId] = useState(initialQaTenantLcaasId || '');
  const [rules, setRules] = useState(initialRules || '');
  const [alerts, setAlerts] = useState(initialAlerts || '');
  const [cardinality, setCardinality] = useState(initialCardinality || '');
  const [apiResponse, setApiResponse] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Function to handle the API call
  const sendDataToFlask = async () => {
    // 1. Construct the JSON payload
    const payload = {
      metrics: metrics,
      Cardinality: cardinality,
      Tenant: qaTenantLcaasId+''+qaTenant,
      rules: rules,
      alerts: alerts
    };

    // Log the payload to see what you're sending (for debugging)
    console.log('Sending JSON Payload:', payload);

    setIsLoading(true);
    setApiResponse(null); // Clear previous response
    setError(null);       // Clear previous error

    try {
      // 2. Make the API call using Axios
      // Replace 'http://127.0.0.1:5000/process_data' with your actual Flask API endpoint
      const response = await axios.post('http://127.0.0.1:5000/process_data', payload, {
        headers: {
          'Content-Type': 'application/json' // Axios often sets this automatically for objects, but good to be explicit
        }
      });

      // 3. Handle the successful response
      console.log('API Response:', response.data);
      setApiResponse(response.data);
    } catch (err) {
      // 4. Handle errors
      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('API Error Response:', err.response.data);
        setError(`Server Error: ${err.response.data.message || 'Unknown server error'}`);
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Network Error: No response received', err.request);
        setError('Network Error: No response from server. Is the Flask API running?');
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Axios Error:', err.message);
        setError(`Client Error: ${err.message}`);
      }
    } finally {
      setIsLoading(false); // End loading regardless of success or failure
    }
  };

