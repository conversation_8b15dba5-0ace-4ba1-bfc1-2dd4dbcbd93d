from verifier_tools import mysql_run_query
import sys
from metrics_director import main as metrics_director_main

 
# Pre-made Queries
def recordings_without_blacklist():
    mysql_run_query("where Recording_Rule='True' and Blacklist_Exists='False'")
def metrics_without_recordings():
    mysql_run_query("where Recording_Rule='False' and Blacklist_Exists='False'") # metrics without recordings


# Query Flag Setup
def main():
    if len(sys.argv) > 2:
        if sys.argv == '-m' or '--metric':
            metric = sys.argv[2]
            print(metric)
            mysql_run_query(f"where Metric_Name='{metric}'") # find specific metric
        if sys.argv == '-s' or '--search':
            fuzzy_metric = sys.argv[2]
            mysql_run_query(f"where Metric_Name LIKE '{fuzzy_metric}%'") # wildcard search 

            #trigger db build and dedup 



if __name__ == "__main__":
    main()