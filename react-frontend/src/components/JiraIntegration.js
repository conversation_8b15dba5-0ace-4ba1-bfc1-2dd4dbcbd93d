import React from 'react';

function JiraIntegration({ onCreateJira, isCreating, ticketInfo, disabled, metrics }) { // Added 'metrics' here
  return (
    <section className="dashboard-section">
      <h2>5. Create Jira Ticket</h2>
      <p>Once all information above is satisfactory, create a Jira ticket to track the onboarding process and SRE review.</p>
      <button onClick={onCreateJira} disabled={isCreating}>
        {isCreating ? 'Creating Ticket...' : 'Create Jira Ticket'}
      </button>
      {isCreating && <div className="loading-spinner"></div>}
      {ticketInfo && (
        <div className="jira-success-message">
          <p>✓ {ticketInfo.message || `Ticket ${ticketInfo.id} created successfully!`}</p> {/* Fallback for message */}
          <p>Ticket ID: <strong>{ticketInfo.id}</strong></p>
          <p>Summary: {ticketInfo.summary}</p>
          {ticketInfo.url && <p><a href={ticketInfo.url} target="_blank" rel="noopener noreferrer">View Ticket</a></p>}
        </div>
      )}
       {disabled && metrics && metrics.length > 0 && ( // Check if metrics is defined
            <p className="message message-warning" style={{marginTop: '10px'}}>
                Please ensure all metrics have been processed through cardinality checks before creating a Jira ticket.
            </p>
        )}
         {disabled && metrics && metrics.length === 0 && ( // Check if metrics is defined
            <p className="message message-info" style={{marginTop: '10px'}}>
                Add and process metrics before creating a Jira ticket.
            </p>
        )}
    </section>
  );
}

export default JiraIntegration;