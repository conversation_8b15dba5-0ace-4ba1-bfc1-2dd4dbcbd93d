import React from 'react';

function MetricListDisplay({ metrics, onRemoveMetric }) {
  if (metrics.length === 0) {
    return <p>No metrics added yet.</p>;
  }

  return (
    <section className="dashboard-section">
      <h3>Added Metrics:</h3>
      {metrics.map(metric => (
        <div key={metric.id} className="metric-item">
          <span>
            <strong>{metric.name}</strong>
            {metric.pod && ` (Pod: ${metric.pod})`}
          </span>
          <button onClick={() => onRemoveMetric(metric.id)} className="remove-btn">Remove</button>
        </div>
      ))}
    </section>
  );
}

export default MetricListDisplay;