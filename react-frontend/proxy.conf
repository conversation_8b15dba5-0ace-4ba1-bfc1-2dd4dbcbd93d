# proxy.conf

# This directive is important to pass the original Host header to the backend server.
ProxyPreserveHost On

# Prevent this server from being used as a forward proxy.
ProxyRequests Off

# Define the reverse proxy rule.
# All traffic to /api/ on this server will be forwarded to http://backend-service:8080/
ProxyPass /api/ http://wss-backend-svc:5001/
ProxyPassReverse /api/ http://wss-backend-svc:5001/