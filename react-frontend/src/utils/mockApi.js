import axios from 'axios';

// Simulates an API call with a delay (kept for other functions)
const simulateApiCall = (data, delay = 700) =>
  new Promise((resolve) => setTimeout(() => resolve(data), delay));

// Real API call for cardinality check and emission status
export const runCardinalityAndEmissionCheck = async (metrics, qaTenant, qaTenantLcaasId) => {
  console.log(`Running cardinality check for ${metrics.length} metrics on ${qaTenant} (LCaaS ID: ${qaTenantLcaasId})...`);

  try {
    // Prepare the payload for the API
    const payload = {
      metrics: metrics,
      env_prefix: qaTenant, // qaTenant == environment
      qaTenantLcaasId: qaTenantLcaasId, // not used for now 
    };

    console.log('Sending JSON Payload to cardinality API:', payload);

    // Make the API call to the Flask backend using axios
    const response = await axios.post('/api/process_data', payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const apiData = response.data;
    console.log('Received API response:', apiData);

    // Process the API response and map it to the expected format
    const results = {};
    metrics.forEach(metric => {
      // Extract cardinality data from API response for this metric
      const metricData = apiData[metric.name] || apiData[metric.id];

      let status = 'OK';
      let message = `Metric ${metric.name} is present and cardinality is within limits on ${qaTenant} (LCaaS ID: ${qaTenantLcaasId}).`;
      let isOmitted = false;

      if (metricData && metricData.cardinality !== undefined) {
        const cardinality = metricData.cardinality;

        // Apply cardinality conditions based on the API response
        if (cardinality > 1000) {
          // Extreme cardinality - Fatal
          status = 'Fatal';
          message = `CRITICAL: Metric ${metric.name} has EXTREME cardinality (${cardinality}) on ${qaTenant}-${qaTenantLcaasId}. Action required.`;
          isOmitted = true;
        } else if (cardinality >= 30 && cardinality <= 1000) {
          // High cardinality - Warning
          status = 'Warning';
          message = `WARNING: Metric ${metric.name} has HIGH CARDINALITY (${cardinality}) on ${qaTenant} (LCaaS ID: ${qaTenantLcaasId}). Review labels.`;
          isOmitted = false;
        } else if (cardinality < 30) {
          // Low cardinality - OK
          status = 'OK';
          message = `Metric ${metric.name} has acceptable cardinality (${cardinality}) on ${qaTenant} (LCaaS ID: ${qaTenantLcaasId}).`;
          isOmitted = false;
        }
      } else if (metricData && metricData.omitted === true) {
        // Metric is not emitted
        status = 'Fatal';
        message = `CRITICAL: Metric ${metric.name} is not emitted on ${qaTenant}-${qaTenantLcaasId}.`;
        isOmitted = true;
      } else if (!metricData) {
        // No data found for metric
        status = 'Warning';
        message = `WARNING: No cardinality data found for metric ${metric.name} on ${qaTenant} (LCaaS ID: ${qaTenantLcaasId}).`;
        isOmitted = false;
      }

      results[metric.id] = {
        status,
        message,
        isOmitted,
        checkedTenant: qaTenant,
        checkedLcaasId: qaTenantLcaasId,
        cardinality: metricData?.cardinality || 0
      };
    });

    return results;

  } catch (error) {
    console.error('Error calling cardinality API:', error);

    // Log more specific error information
    if (error.response) {
      console.error('API responded with error status:', error.response.status);
      console.error('API error data:', error.response.data);
    } else if (error.request) {
      console.error('No response received from API:', error.request);
    } else {
      console.error('Error setting up API request:', error.message);
    }

    // Fallback to mock behavior if API fails
    console.log('Falling back to mock behavior due to API error');
    const results = {};
    metrics.forEach(metric => {
      results[metric.id] = {
        status: 'Warning',
        message: `Unable to check cardinality for ${metric.name} on ${qaTenant} (LCaaS ID: ${qaTenantLcaasId}). API unavailable: ${error.message}`,
        isOmitted: false,
        checkedTenant: qaTenant,
        checkedLcaasId: qaTenantLcaasId,
        cardinality: 0
      };
    });
    return results;
  }
};

// Simulates generating a recording rule example
export const generateRecordingRuleExample = async (metricName, selectedLabels) => {
  // Check if this is a bucket metric (histogram)
  const isBucketMetric = metricName.endsWith('_bucket');

  if (isBucketMetric) {
    // Generate multiple histogram recording rules for bucket metrics
    const example = `recording_rule_for_${metricName}:
  - record: tenant:${metricName}:p90
    expr: histogram_quantile(0.9, sum(rate(${metricName}[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))

  - record: tenant:${metricName}:p95
    expr: histogram_quantile(0.95, sum(rate(${metricName}[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))

  - record: tenant:${metricName}:p99
    expr: histogram_quantile(0.99, sum(rate(${metricName}[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))`;
    return simulateApiCall({ needed: true, example });
  }

  if (!selectedLabels || selectedLabels.length === 0) {
    return simulateApiCall({ // Make sure to use simulateApiCall here too
      needed: false,
      example: "/* No labels selected, or recording rule may not be necessary . */"
    });
  }

  const baseMetricName = metricName.replace(/[^a-zA-Z0-9_]/g, '_');
  const newMetricName = `${baseMetricName}_by_${selectedLabels.join('_')}`;
  const labelsString = selectedLabels.join(', ');

  const example = `
${metricName}:
  - record: tenant:${metricName}:sum
    expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id)(${metricName})`;
  return simulateApiCall({ needed: true, example }); // And here
};

// Simulates generating alert examples
export const generateAlertExamples = async (metricName, selectedLabels, cardinalityStatus) => {
  const baseMetricNameForAlert = metricName.replace(/[^a-zA-Z0-9_]/g, '_');
  let targetMetric = metricName;
  if (cardinalityStatus && cardinalityStatus.status !== 'Fatal' && selectedLabels && selectedLabels.length > 0) {
      targetMetric = `${baseMetricNameForAlert}_by_${selectedLabels.join('_')}`;
  }

  const examples = [
    {
      name: `Xpanse-High-Rate-${baseMetricNameForAlert.charAt(0).toUpperCase() + baseMetricNameForAlert.slice(1)}-Last-5m`,
      expr: `sum by (tenant_id)((rate(${targetMetric}{job="your-job"}[5m]))) > 100`,
      forDuration: '5m',
      severity: 'warning',
      summary: `High rate reporting for ${metricName} on {{ $labels.instance }}.`,
      description: `The metric ${targetMetric} on instance {{ $labels.instance }} is reporting a rate of {{ $value }}, exceeding the threshold.`,
      runbook:'confluence link',
    },
    {
      name: `XDR-Missing-Metric-${baseMetricNameForAlert.charAt(0).toUpperCase() + baseMetricNameForAlert.slice(1)}Absent`,
      expr: `absent(${targetMetric}{job="your-job"})`,
      forDuration: '10m',
      severity: 'critical',
      summary: `Metric ${metricName} is absent for job 'your-job'.`,
      description: `The metric ${targetMetric} (or its aggregated form) has not been seen for the last 10 minutes for job 'your-job'.`,
      runbook:'confluence link',
    }
  ];

  if (cardinalityStatus && cardinalityStatus.status === 'Warning') {
      examples.unshift({
          name: `XDR-High-Error-Rate-${baseMetricNameForAlert.charAt(0).toUpperCase() + baseMetricNameForAlert.slice(1)}`,
          expr: `sum by (tenant_id) (${metricName})) > 5000`,
          forDuration: '1h',
          severity: 'info',
          summary: `High cardinality detected for ${metricName}.`,
          description: `The metric ${metricName} has a high number of series. Investigate label selection or consider aggregation. Current series count: {{ $value }}.`,
          runbook:'confluence link',
      });
  }

  const alertYaml = examples.map(alert => `
  - alert: ${alert.name}
    expr: ${alert.expr}
    for: ${alert.forDuration}
    labels:
      pd_service: < Specify > 
      severity: ${alert.severity}
    annotations:
      summary: ${alert.summary}
      description: ${alert.description}
`).join('');

  return simulateApiCall({
    message: "Below are some common alert patterns. Adjust expressions, thresholds, and labels as per your specific requirements. SRE review is recommended.",
    alerts: alertYaml
  });
};

// Helper function to filter out metric IDs from the metrics array
const filterMetricIds = (metrics) => {
  return metrics.map(metric => {
    // Create a new object without the 'id' property
    const { id, ...metricWithoutId } = metric;
    return metricWithoutId;
  });
};

// Real API call for creating a Jira ticket
export const createJiraTicketApi = async (jiraPayload, additionalData = {}) => {
  console.log("Creating Jira Ticket with payload:", jiraPayload);
  console.log("Additional data:", additionalData);

  try {
    // Extract additional data for the API call
    const {
      lcaas = '',
      metrics = [],
      rules = '',
      alerts = '',
      cardinality = {},
      slackLinks = [],
      qaTenant = '',
      targetBranch = ''
    } = additionalData;

    // Filter out metric IDs before sending to API
    const filteredMetrics = filterMetricIds(metrics);
    console.log('Original metrics:', metrics);
    console.log('Filtered metrics (IDs removed):', filteredMetrics);

    // Prepare the payload for the Flask API
    const apiPayload = {
      summary: jiraPayload.summary || 'Metrics Onboarding Request',
      description: jiraPayload.description || 'Automated metrics onboarding request',
      project_id: lcaas, // lcaas == project_id
      environment: qaTenant || '',
      target_branch: targetBranch || '',
      metrics: filteredMetrics, // Use filtered metrics without IDs
      rules: rules,
      // alerts: alerts,
      // cardinality: cardinality,
      slackLinks: slackLinks,
      // Additional fields that might be useful
    };

    console.log('Sending JSON Payload to Jira API:', apiPayload);

    // Make the API call to the Flask backend using axios
    const response = await axios.post('/api/create_jira_issue', apiPayload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const apiData = response.data;
    console.log('Received Jira API response:', apiData);

    // Map the API response to the expected format
    return {
      success: true,
      message: apiData.message || 'Jira ticket created successfully!',
      ticketInfo: {
        id: apiData.key || apiData.issue_key || 'Unknown',
        url: 'https://jira-dc.paloaltonetworks.com/browse/' + apiData.key || '#',
        summary: jiraPayload.summary,
        key: apiData.key || 'Unknown'
      }
    };

  } catch (error) {
    console.error('Error calling Jira API:', error);

    // Log more specific error information
    if (error.response) {
      console.error('API responded with error status:', error.response.status);
      console.error('API error data:', error.response.data);
    } else if (error.request) {
      console.error('No response received from API:', error.request);
    } else {
      console.error('Error setting up API request:', error.message);
    }

    // Return error response in expected format
    return {
      success: false,
      message: `Failed to create Jira ticket: ${error.response?.data?.error || error.message}`,
      ticketInfo: null,
      error: error.response?.data || error.message
    };
  }
};