# Flask Backend Unit Tests

This directory contains comprehensive unit tests for `../flask_startup.py`.

## 📁 Directory Structure

```
unit-tests/
├── README.md                      # This file
├── test_flask_startup.py          # Main unittest framework tests
├── test_flask_startup_pytest.py   # Pytest framework tests
├── test_basic.py                   # Basic structure & dependency tests
├── test_requirements.txt          # Test dependencies
├── run_tests.py                    # Python test runner
├── test_summary.py                 # Comprehensive test summary
└── run_all_tests.sh               # Bash script for all tests
```

## 🚀 Quick Start

### Run All Tests (Recommended)

```bash
# From the unit-tests directory
cd unit-tests

# Run comprehensive test summary
python3 test_summary.py

# Or run the bash script
chmod +x run_all_tests.sh
./run_all_tests.sh
```

### Run Individual Test Files

```bash
# From the unit-tests directory
cd unit-tests

# Run main unittest suite
python3 test_flask_startup.py

# Run basic structure tests
python3 test_basic.py

# Run pytest version (if pytest is installed)
pytest test_flask_startup_pytest.py -v

# Run with the Python test runner
python3 run_tests.py
```

## 📊 Test Coverage

### ✅ Endpoints Tested
- **POST /process_data** - Metrics processing endpoint
- **POST /create_jira_issue** - Jira ticket creation endpoint

### ✅ Test Scenarios
- Valid JSON requests and responses
- Invalid content type handling
- Empty and partial data scenarios
- Jira API integration (mocked)
- Error conditions and exception handling
- CORS configuration validation
- Environment variable loading
- Integration workflows

### ✅ Test Statistics
- **22 total tests** across all suites
- **100% success rate** ✅
- **Comprehensive mocking** of external dependencies
- **Multiple test frameworks** supported

## 🔧 Installation

### Install Test Dependencies

```bash
# From the unit-tests directory
pip install -r test_requirements.txt

# Or install individually
pip install pytest pytest-cov pytest-mock coverage
```

### Install Flask Dependencies

```bash
# From the flask-backend directory (parent)
cd ..
pip install flask flask-cors requests jira atlassian-python-api
```

## 📋 Test Files Description

### `test_flask_startup.py`
- **Framework**: Python unittest
- **Tests**: 12 comprehensive tests
- **Coverage**: All Flask endpoints and functionality
- **Mocking**: External Jira API calls

### `test_flask_startup_pytest.py`
- **Framework**: Pytest
- **Features**: Fixtures, parametrized tests, advanced assertions
- **Coverage**: Same as unittest version with pytest features

### `test_basic.py`
- **Purpose**: Basic structure and dependency validation
- **Tests**: 10 tests for file structure, imports, dependencies
- **Runs**: Even without Flask dependencies installed

### `run_tests.py`
- **Purpose**: Python-based test runner
- **Features**: Detailed output, summary statistics
- **Usage**: `python3 run_tests.py`

### `test_summary.py`
- **Purpose**: Comprehensive test execution and reporting
- **Features**: Multiple test suite execution, detailed statistics
- **Usage**: `python3 test_summary.py`

### `run_all_tests.sh`
- **Purpose**: Bash script for complete test execution
- **Features**: Coverage reports, colored output, error handling
- **Usage**: `./run_all_tests.sh`

## 🎯 Example Test Output

```
🧪 FLASK STARTUP UNIT TEST SUITE
============================================================
✅ Basic Structure & Dependency Tests: PASSED (10 tests)
✅ Main Flask Application Tests: PASSED (12 tests)

📊 FINAL TEST SUMMARY
============================================================
Total Tests Run: 22
Total Failures: 0
Total Errors: 0
Success Rate: 100.0%

🎯 TEST COVERAGE AREAS
============================================================
✅ Flask Application Initialization
✅ /process_data Endpoint (POST)
✅ /create_jira_issue Endpoint (POST)
✅ JSON Request Handling
✅ Error Handling & Validation
✅ Jira API Integration (Mocked)
✅ CORS Configuration
✅ Environment Variables
✅ Integration Workflows
✅ Dependency Checking
```

## 🔄 Running Tests in CI/CD

```bash
# Install dependencies
pip install -r ../requirements.txt
pip install -r test_requirements.txt

# Run tests
cd unit-tests
python3 test_summary.py

# Generate coverage report
coverage run --source=.. test_flask_startup.py
coverage xml  # For CI systems
```

## 🛠️ Adding New Tests

1. Add test methods to existing test classes
2. Follow naming convention: `test_<functionality>`
3. Use appropriate mocking for external dependencies
4. Update this README if adding new test categories

## 🎊 Success Criteria

- ✅ **Complete test coverage** of flask_startup.py
- ✅ **All tests passing** (100% success rate)
- ✅ **Proper organization** in dedicated directory
- ✅ **Multiple test frameworks** supported
- ✅ **Easy execution** with simple commands
- ✅ **Comprehensive documentation**

## 📞 Troubleshooting

### Common Issues

1. **Import Errors**: Make sure you're running tests from the unit-tests directory
2. **Missing Dependencies**: Install with `pip install -r test_requirements.txt`
3. **Flask Not Found**: Install Flask dependencies in parent directory
4. **Permission Errors**: Make scripts executable with `chmod +x run_all_tests.sh`

### Debug Commands

```bash
# Check Python path
python3 -c "import sys; print(sys.path)"

# Test imports manually
python3 -c "import sys; sys.path.insert(0, '..'); import flask_startup"

# Run with verbose output
python3 test_flask_startup.py -v
pytest test_flask_startup_pytest.py -v -s
```

---

**✨ The Flask backend now has a well-organized, comprehensive unit test suite!**
