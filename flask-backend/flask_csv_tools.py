# flask csv read
# flask trigger db rebuild ?
# flask cardinality trigger 
# read slack_bot responce and parse cardinality per pod and cardinality max  
# import logging
# import sys
# import time
# from jira import JIRA
from typing import Mapping

# def jira_connect(jira_user, jira_pass=None):
#     logging.debug("Attempting login to Jira")
#     options = {
#         'server': 'jsonscrit',
#         'verify': False
#     }
#     max_attempts = 3
#     i = 0
#     while i < max_attempts:
#         i = i + 1
#         try:
#             logging.debug("Authenticate to Jira with Token")
#             jra = JIRA(token_auth=jira_pass, options=options)
#             i = i + max_attempts
#             logging.debug("Login to Jira successful")
#             return jra
#         except Exception as err:
#             if i > max_attempts:
#                 logging.critical("Error logging to Jira, error is %s" % err)
#                 sys.exit(1)
#             logging.debug("Error logging to Jira, error is %s" % err)
#             time.sleep(10)
#     logging.critical("Login to Jira FAILED!")
#     return None

def jira_custom_field_mapping(self) -> Mapping[str, str]:
        return {
            "cortexproduct": "customfield_19558",
            "cdl": "customfield_19136",
            "cdlregion": "customfield_19127",
            "actualbehavior": "customfield_19721",
            "reproducible": "customfield_19704",
            "customername": "customfield_16201",
            "affectedversions": "versions",
            "case": "customfield_18918",
        }


