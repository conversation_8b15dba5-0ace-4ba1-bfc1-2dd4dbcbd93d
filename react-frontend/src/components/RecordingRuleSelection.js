import React, { useState, useEffect } from 'react';
import { generateRecordingRuleExample } from '../utils/mockApi';

function RecordingRuleSelection({
  metrics,
  selectedLabels,
  cardinalityResults,
  onRecordingRuleToggle,
  recordingRuleSelections,
  onRecordingRuleRecommendation,
  onRecordingRuleEdit // New prop for handling edits
}) {
  const [isLoadingRules, setIsLoadingRules] = useState(false);
  const [recordingRuleRecommendations, setRecordingRuleRecommendations] = useState({});

  // Define generateRecommendation function first
  const generateRecommendation = async (metricId) => {
    const metric = metrics.find(m => m.id === metricId);
    if (!metric) {
      console.log('Metric not found for ID:', metricId);
      return;
    }

    console.log('Generating recommendation for metric:', metric.name, 'with labels:', selectedLabels[metricId] || []);
    setIsLoadingRules(true);
    try {
      const ruleData = await generateRecordingRuleExample(
        metric.name,
        selectedLabels[metricId] || []
      );

      console.log('Generated rule data:', ruleData);
      setRecordingRuleRecommendations(prev => ({
        ...prev,
        [metricId]: ruleData
      }));

      onRecordingRuleRecommendation(metricId, ruleData);
    } catch (error) {
      console.error('Error generating recording rule recommendation:', error);
    } finally {
      setIsLoadingRules(false);
    }
  };

  // Auto-select metrics with _bucket suffix
  useEffect(() => {
    console.log('RecordingRuleSelection: Checking for bucket metrics...', metrics.map(m => m.name));

    metrics.forEach(metric => {
      if (metric.name.endsWith('_bucket')) {
        console.log('Found bucket metric:', metric.name, 'Already selected:', recordingRuleSelections[metric.id]);
        const isAlreadySelected = recordingRuleSelections[metric.id];
        if (!isAlreadySelected) {
          console.log('Auto-selecting bucket metric:', metric.name);
          // Auto-select the checkbox
          onRecordingRuleToggle(metric.id, true);
          // Auto-generate recommendation
          setTimeout(() => {
            generateRecommendation(metric.id);
          }, 100); // Small delay to ensure state is updated
        }
      }
    });
  }, [metrics, recordingRuleSelections]);

  // Check if a metric has _bucket suffix (should be auto-selected and disabled)
  const isBucketMetric = (metricName) => {
    return metricName.endsWith('_bucket');
  };

  const handleCheckboxChange = (metricId, isChecked) => {
    onRecordingRuleToggle(metricId, isChecked);

    if (isChecked) {
      // Generate recommendation when checkbox is selected
      generateRecommendation(metricId);
    } else {
      // Clear recommendation when checkbox is unchecked
      setRecordingRuleRecommendations(prev => {
        const newRecommendations = { ...prev };
        delete newRecommendations[metricId];
        return newRecommendations;
      });
      onRecordingRuleRecommendation(metricId, null);
    }
  };

  // Handle editing of the recording rule text
  const handleRuleEdit = (metricId, newRuleText) => {
    // Update local state
    setRecordingRuleRecommendations(prev => ({
      ...prev,
      [metricId]: {
        ...prev[metricId],
        example: newRuleText
      }
    }));

    // Notify parent component if handler is provided
    if (onRecordingRuleEdit) {
      onRecordingRuleEdit(metricId, newRuleText);
    }
  };

  // Filter out metrics with Fatal status
  const actionableMetrics = metrics.filter(m => cardinalityResults[m.id]?.status !== 'FatalX');

  if (actionableMetrics.length === 0 && metrics.length > 0) {
    return (
      <section className="dashboard-section">
        <h2>3. Recording Rule Selection</h2>
        <p className="message message-info">No metrics available for recording rule selection (either all have Fatal issues or none processed yet).</p>
      </section>
    );
  }

  if (metrics.length === 0) {
    return null;
  }

  return (
    <section className="dashboard-section">
      <h2>3. Recording Rule Selection</h2>
      <p className="message message-info">
        Select metrics that require recording rules. Recording rules help reduce query load by pre-computing aggregations.
      </p>
      
      {actionableMetrics.map(metric => {
        const metricCardinalityStatus = cardinalityResults[metric.id]?.status;
        const isSelected = recordingRuleSelections[metric.id] || false;
        const recommendation = recordingRuleRecommendations[metric.id];
        const isBucket = isBucketMetric(metric.name);

        return (
          <div key={metric.id} className="recording-rule-selection-container">
            <div className="metric-header">
              <h4>
                Metric: {metric.name}
                {metric.pod ? ` (Pod: ${metric.pod})` : ''}
                {metricCardinalityStatus && ` - Status: ${metricCardinalityStatus}`}
                {isBucket && <span style={{color: 'var(--primary-green)', fontWeight: 'bold'}}> (Bucket Metric , Needs Recording Rule)</span>}
              </h4>

              {metricCardinalityStatus === 'Warning' && (
                <p className="message message-warning" style={{fontSize: '0.9em', padding: '8px'}}>
                  This metric has a cardinality warning. A recording rule may help reduce query load.
                </p>
              )}

              {isBucket && (
                <p className="message message-info" style={{fontSize: '0.9em', padding: '8px'}}>
                  Recording rule is automatically required for bucket metrics and cannot be disabled.
                </p>
              )}
            </div>

            <div className="recording-rule-checkbox">
              <label style={{opacity: isBucket ? 0.7 : 1}}>
                <input
                  type="checkbox"
                  checked={isSelected}
                  disabled={isBucket}
                  onChange={(e) => handleCheckboxChange(metric.id, e.target.checked)}
                />
                <strong> Generate Recording Rule for this metric</strong>
                {isBucket && <span style={{fontSize: '0.85em', color: 'var(--text-color-secondary)'}}> (Auto-selected)</span>}
              </label>
            </div>

            {isSelected && (
              <div className="recording-rule-recommendation">
                {isLoadingRules ? (
                  <div className="loading-spinner">Generating recording rule recommendation...</div>
                ) : recommendation ? (
                  <div className="recommendation-content">
                    <h5>Recording Rule Recommendation:</h5>
                    {recommendation.needed ? (
                      <div>
                        <p className="message message-info">
                          A recording rule is recommended for this metric. Please review and edit the rule below.
                          <strong> SRE review is required.</strong>
                        </p>
                        <textarea
                          className="editable-rule-yaml"
                          value={recommendation.example || ""}
                          onChange={(e) => handleRuleEdit(metric.id, e.target.value)}
                          rows={12}
                          spellCheck="false"
                          placeholder="Enter or modify recording rule YAML here..."
                        />
                      </div>
                    ) : (
                      <p className="message message-ok">
                        {recommendation.example}
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="message message-info">Click the checkbox to generate a recording rule recommendation.</p>
                )}
              </div>
            )}

            {selectedLabels[metric.id]?.length > 0 && (
              <div className="selected-labels-info">
                <small><strong>Selected Labels:</strong> {selectedLabels[metric.id].join(', ')}</small>
              </div>
            )}
          </div>
        );
      })}
    </section>
  );
}

export default RecordingRuleSelection;
