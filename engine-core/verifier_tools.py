import pickle
import pandas as pd
import yaml
import os
import json
import io
import csv
import mysql.connector
GREEN = '\033[92m'
COLOR_END = '\033[0m'
LIGHT_YELLOW = '\033[93m'
LIGHT_BLUE = '\033[94m'
TURQUOISE = '\033[96m'
# ANSI escape code to reset text color
whitelist_file = '/Users/<USER>/Documents/Repos/gcp-configuration/configengine/charts/tenant/prometheus/files/whitelist.yaml'
db_file = 'tools/whitelist-verifier/data.json'
blacklist_file = "/Users/<USER>/Documents/Repos/prometheus-config/ansible/vars-shared.yml"
recordings_file = '/Users/<USER>/Documents/Repos/gcp-configuration/configengine/charts/tenant/prometheus/files/prometheus.rules'
def open_db():
    with open(db_file, "r") as file:
        data = json.load(file)
        return data

def merge_dicts(*dict_args):
    result = {}
    for dictionary in dict_args:
        result.update(dictionary)
    return result

def write_db(new_dictionary):
    with open('data.json', 'w') as outfile:
        json.dump(new_dictionary, outfile, indent=4)


def enumerate_whitelist_entries():
    with open(whitelist_file, 'r') as file_yaml:
        yaml_data = yaml.safe_load(file_yaml)
        return yaml_data['kubernetes-pods']['regex']

def enumerate_blacklist_entries(metric):
    with open(blacklist_file, 'r') as file_yaml2:
        yaml_data = yaml.safe_load(file_yaml2)
        new_data = yaml_data['tenant_metrics_match_params']
        # text cleanup 
        new_list = [str(i).replace( '__name__!="', '') for i in new_data]
        new_list = [str(i).replace( '__name__!=~"', '') for i in new_data]
        new_list = [str(i).replace( '__name__!=', '') for i in new_list]
        new_list = [str(i).replace( '__name__!~', '') for i in new_list]
        new_list = [str(i).replace( '"', '') for i in new_list]
        if metric in new_list:
            return True
        else:
            return False
    
     
def enumerate_recording_entries():
    with open(recordings_file, 'r') as file_yaml3:
        yaml_data = yaml.safe_load(file_yaml3)
        return yaml_data

def does_metric_have_recording_rules(metric):
    command = f"grep -w -q {metric} {recordings_file}"
    result = os.system(command)
    if result == 0:
        return True
    else:
        return False
        

def does_metric_have_blacklist(metric):
    command = f"sudo grep -w -q {metric} {blacklist_file}"
    result = os.system(command)
    if result == 0:
        return True
    else:
        return False
# def does_metric_have_blacklist_entry(metric, list: list):
#     if metric in list:
#         return True


def is_string_in_yaml(string, yaml_data):
    with open(yaml_data) as f:
        contents = f.read()
        if string in contents:
            return True
        else:
            return False
        

def write_csv_db(data , metric):
    csv_field_data = ['Metric Name', 'Recording Rule', 'Blacklist_Exists']
    filename = 'engine-core/data_dict.csv'
    with open(filename, 'a', newline='') as file:
        writer = csv.DictWriter(file, fieldnames=csv_field_data)
        writer.writerows([data])

def dedup_csv_rows():
    filename = 'engine-core/data_dict.csv'
    with open(filename, 'r') as file:
        reader = csv.reader(file)
        header = next(reader)
        rows = list(reader)
        merged_rows = {}
        for row in rows:
            key = row[0]  # Assuming the first column is the key for merging
            if key in merged_rows:
            # Merge the current row with the existing row
                for i in range(1, len(row)):  # Iterate through the rest of the columns
                    if row[i]:  # If the current cell is not empty
                        merged_rows[key][i] = row[i]
            else:
                merged_rows[key] = row

def clean_csv_file_prepare_for_mysql(csv_file):
    with open(csv_file, 'r') as f_input, open('tools/whitelist-verifier/db_ready_for_import.csv', 'w') as f_output:
        for line in f_input:
            cleaned_line = line.replace('{', '').replace('}', '')
            cleaned_line = cleaned_line.replace("'", "")
            f_output.write(cleaned_line)

def mysql_run_query(query_condition):
    # the user and password here are just demo and only exist on my local machine 
    db = mysql.connector.connect(user='root', password='123qwe', host='127.0.0.1',database='metrics')
    cur = db.cursor()
    print(LIGHT_BLUE+(f" Running Query --> SELECT * FROM metrics.metrics_table {query_condition}")+ COLOR_END)
    cur.execute(f"SELECT * FROM metrics.metrics_table {query_condition}")
    print(TURQUOISE+('Metric_Name   '+'              Recording_rule'+'    Blacklist')+COLOR_END)
    for row in cur.fetchall():
        box_width = len(row[0]) + 10
        print(GREEN+('*' * box_width)+COLOR_END)
        print(LIGHT_YELLOW+(row[0]+',   '+row[1]+',   '+row[2])+ COLOR_END)
        print(GREEN+('*' * box_width)+COLOR_END)
    db.close()
def deduplicate_csv(csv_filepath):
    """
    Removes duplicate rows from a CSV file in place.

    Args:
        csv_filepath (str): The path to the CSV file.
    """
    try:
        df = pd.read_csv(csv_filepath)
        df.drop_duplicates(inplace=True)
        df.to_csv(csv_filepath, index=False)
    except FileNotFoundError:
        print(f"Error: File not found: {csv_filepath}")
    except pd.errors.EmptyDataError:
         print(f"Error: CSV file is empty: {csv_filepath}")
    except Exception as e:
        print(f"An error occurred: {e}")




