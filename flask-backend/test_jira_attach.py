import os
from atlassian import <PERSON><PERSON>
from io import StringIO, BytesIO
JIRA_TOKEN = os.getenv("JIRA_TOKEN")
JIRA_URL = "https://jira-dc.paloaltonetworks.com/"
JIRA_PROJECT_KEY = "CRTX"
JIRA_ISSUE_TYPE_NAME = "Task"

    
jira = Jira(token=JIRA_TOKEN, url=JIRA_URL)

issue_id = "CRTX-193839"
attachment_content = "{'recording_rule_for_test': [{'record': 'tenant:test:sum', 'expr': 'sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id)(test)'}, {'record': 'tenant:test:avg', 'expr': 'avg by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id)(test)'}], 'recording_rule_for_test_bucket': [{'record': 'tenant:test_bucket:p90', 'expr': 'histogram_quantile(0.9, sum(rate(test_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))'}, {'record': 'tenant:test_bucket:p95', 'expr': 'histogram_quantile(0.95, sum(rate(test_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))'}, {'record': 'tenant:test_bucket:p99', 'expr': 'histogram_quantile(0.99, sum(rate(test_bucket[5m])) by (le, kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id))'}]}"
attachment_filename = "requested-recordings.yaml"
# write attachment_content to a file
with open(attachment_filename, 'w') as f:
    f.write(attachment_content)

print("adding attachment . . . . . . . . . ")

jira.add_attachment(issue_key=issue_id, filename=attachment_filename)
# jira.add_attachment_object(issue_key=issue_id, attachment=attachment_content)
print("attachment added!")