{"summary": "Metrics Onboarding Request for  ST MAIN qa2-test-1299123)", "description": "\n{\"target_version\":\"Next Version (v3.16)\",\"environment\": \"ST MAIN\",\"project_id\": \"qa2-test-1299123\"}\n\n\"Metric\": \"asdasdasdasd\"\"recording_rule_for\" : \"asdasdasdasd\"\n  - record: tenant:asdasdasdasd:sum\n    expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id)(asdasdasdasd)\n\"Metric\": \"ping\"\"recording_rule_for\" : \"ping\"\n  - record: tenant:ping:sum\n    expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id)(ping)\n", "lcaas": "qa2-test-1299123", "metrics": [{"id": "metric-0", "name": "<PERSON><PERSON><PERSON>das<PERSON>", "pod": ""}, {"id": "metric-1", "name": "ping", "pod": ""}], "rules": "\n\"recording_rule_for\" : \"asdasdasdasd\"\n  - record: tenant:asdasdasdasd:sum\n    expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id)(asdasdasdasd)\n\n\n\"recording_rule_for\" : \"ping\"\n  - record: tenant:ping:sum\n    expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id)(ping)", "slackLinks": []}