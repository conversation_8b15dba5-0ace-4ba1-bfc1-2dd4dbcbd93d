#!/bin/bash

# Test runner script for flask_startup.py
# This script runs both unittest and pytest versions of the tests
# Run from the unit-tests directory

set -e

echo "=========================================="
echo "FLASK STARTUP UNIT TESTS"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "../flask_startup.py" ]; then
    print_error "flask_startup.py not found in parent directory. Please run this script from the unit-tests directory."
    exit 1
fi

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed or not in PATH"
    exit 1
fi

print_status "Starting test execution from unit-tests directory..."

# Run unittest version
echo ""
echo "=========================================="
echo "RUNNING UNITTEST VERSION"
echo "=========================================="

if python3 test_flask_startup.py; then
    print_status "✅ Unittest tests passed!"
    UNITTEST_RESULT=0
else
    print_error "❌ Unittest tests failed!"
    UNITTEST_RESULT=1
fi

echo ""
echo "=========================================="
echo "RUNNING PYTEST VERSION (if available)"
echo "=========================================="

# Check if pytest is available
if command -v pytest &> /dev/null; then
    if pytest test_flask_startup_pytest.py -v --tb=short; then
        print_status "✅ Pytest tests passed!"
        PYTEST_RESULT=0
    else
        print_error "❌ Pytest tests failed!"
        PYTEST_RESULT=1
    fi
else
    print_warning "Pytest not available. Install with: pip install pytest"
    print_status "Running basic pytest simulation with python..."
    if python3 test_flask_startup_pytest.py; then
        print_status "✅ Pytest simulation passed!"
        PYTEST_RESULT=0
    else
        print_error "❌ Pytest simulation failed!"
        PYTEST_RESULT=1
    fi
fi

# Generate coverage report if coverage is available
echo ""
echo "=========================================="
echo "COVERAGE REPORT (if available)"
echo "=========================================="

if command -v coverage &> /dev/null; then
    print_status "Generating coverage report..."
    coverage run --source=.. test_flask_startup.py
    coverage report -m
    coverage html --directory=htmlcov
    print_status "HTML coverage report generated in htmlcov/"
else
    print_warning "Coverage not available. Install with: pip install coverage"
fi

# Final summary
echo ""
echo "=========================================="
echo "FINAL TEST SUMMARY"
echo "=========================================="

if [ $UNITTEST_RESULT -eq 0 ] && [ $PYTEST_RESULT -eq 0 ]; then
    print_status "🎉 ALL TESTS PASSED!"
    echo ""
    echo "📁 Test files are now organized in unit-tests/ directory:"
    echo "   - test_flask_startup.py (main unittest suite)"
    echo "   - test_flask_startup_pytest.py (pytest version)"
    echo "   - test_basic.py (basic structure tests)"
    echo "   - test_requirements.txt (test dependencies)"
    echo "   - run_tests.py (Python test runner)"
    echo "   - test_summary.py (comprehensive test summary)"
    echo ""
    echo "📚 To run tests:"
    echo "   cd unit-tests"
    echo "   python3 test_summary.py"
    echo "   python3 test_flask_startup.py"
    echo "   pytest test_flask_startup_pytest.py -v"
    exit 0
else
    print_error "💥 SOME TESTS FAILED!"
    echo "Unittest result: $UNITTEST_RESULT"
    echo "Pytest result: $PYTEST_RESULT"
    exit 1
fi
