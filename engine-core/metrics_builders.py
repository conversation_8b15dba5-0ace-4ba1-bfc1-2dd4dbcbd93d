
# here the check logic needs to be stup for each element 

from verifier import MetricObject
from verifier_tools import enumerate_blacklist_entries, does_metric_have_blacklist , does_metric_have_recording_rules

class MetricsBuilder:
    def __init__(self):
        self._metric = MetricObject()

    def add_metric_name(self, metric: str):
        self._metric.set_metric_name(metric)
        return self
    # add_rr should take metric - then search for recording rules 
    
    def add_rr(self, metric):
        recordings = does_metric_have_recording_rules(metric)
        if recordings == True:
            self._metric.set_rr(True)
        return self

    def add_blacklist(self):
        blacklist_list = does_metric_have_blacklist(self._metric._metric_name)
        if blacklist_list:
            self._metric.set_blacklist(True)
        else:
            self._metric.set_blacklist(False)
        return self
    # append f'{datetime.now(),note }'
    # def set_note(self, note: str):
    #     self._metric.set_note().append(note)
    #     return self
    
    def build(self):
        return self._metric
    
