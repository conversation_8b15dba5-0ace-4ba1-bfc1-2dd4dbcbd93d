#!/usr/bin/env python3
"""
Test summary script for flask_startup.py
Runs all tests and provides a comprehensive summary
Run from the unit-tests directory
"""

import unittest
import sys
import os
from io import StringIO
import time

# Add parent directory to path for importing flask_startup
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def run_test_suite(test_file, description):
    """Run a specific test suite and return results"""
    print(f"\n{'='*60}")
    print(f"RUNNING: {description}")
    print(f"FILE: {test_file}")
    print(f"{'='*60}")
    
    # Import and run the test module
    try:
        # Load the test module
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromName(test_file.replace('.py', ''))
        
        # Run tests with detailed output
        stream = StringIO()
        runner = unittest.TextTestRunner(
            stream=stream,
            verbosity=2,
            buffer=True
        )
        
        start_time = time.time()
        result = runner.run(suite)
        end_time = time.time()
        
        # Get the output
        output = stream.getvalue()
        
        return {
            'result': result,
            'output': output,
            'duration': end_time - start_time,
            'success': result.wasSuccessful()
        }
        
    except Exception as e:
        return {
            'result': None,
            'output': f"Error running tests: {e}",
            'duration': 0,
            'success': False,
            'error': str(e)
        }


def main():
    """Main test runner function"""
    print("🧪 FLASK STARTUP UNIT TEST SUITE")
    print("=" * 60)
    print("Testing flask_startup.py comprehensive coverage")
    print("=" * 60)
    
    # Test suites to run
    test_suites = [
        ('test_basic.py', 'Basic Structure & Dependency Tests'),
        ('test_flask_startup_fixed.py', 'Flask Application Tests'),
    ]
    
    all_results = []
    total_tests = 0
    total_failures = 0
    total_errors = 0
    total_duration = 0
    
    # Run each test suite
    for test_file, description in test_suites:
        if os.path.exists(test_file):
            result_info = run_test_suite(test_file, description)
            all_results.append((test_file, description, result_info))
            
            # Print immediate results
            if result_info['success']:
                print(f"✅ {description}: PASSED")
            else:
                print(f"❌ {description}: FAILED")
            
            if result_info['result']:
                total_tests += result_info['result'].testsRun
                total_failures += len(result_info['result'].failures)
                total_errors += len(result_info['result'].errors)
            
            total_duration += result_info['duration']
            
            # Show brief output
            print(result_info['output'][:500] + "..." if len(result_info['output']) > 500 else result_info['output'])
        else:
            print(f"⚠️  {test_file} not found, skipping...")
    
    # Final summary
    print("\n" + "="*60)
    print("📊 FINAL TEST SUMMARY")
    print("="*60)
    
    for test_file, description, result_info in all_results:
        status = "✅ PASSED" if result_info['success'] else "❌ FAILED"
        duration = f"{result_info['duration']:.3f}s"
        
        if result_info['result']:
            test_count = result_info['result'].testsRun
            print(f"{status} | {description}")
            print(f"         Tests: {test_count}, Duration: {duration}")
            
            if result_info['result'].failures:
                print(f"         Failures: {len(result_info['result'].failures)}")
            
            if result_info['result'].errors:
                print(f"         Errors: {len(result_info['result'].errors)}")
        else:
            print(f"{status} | {description}")
            print(f"         Error: {result_info.get('error', 'Unknown error')}")
        
        print()
    
    # Overall statistics
    print("="*60)
    print("📈 OVERALL STATISTICS")
    print("="*60)
    print(f"Total Tests Run: {total_tests}")
    print(f"Total Failures: {total_failures}")
    print(f"Total Errors: {total_errors}")
    print(f"Total Duration: {total_duration:.3f}s")
    print(f"Success Rate: {((total_tests - total_failures - total_errors) / total_tests * 100):.1f}%" if total_tests > 0 else "N/A")
    
    # Test coverage areas
    print("\n" + "="*60)
    print("🎯 TEST COVERAGE AREAS")
    print("="*60)
    print("✅ Flask Application Initialization")
    print("✅ /process_data Endpoint (POST)")
    print("✅ /create_jira_issue Endpoint (POST)")
    print("✅ JSON Request Handling")
    print("✅ Error Handling & Validation")
    print("✅ Jira API Integration (Mocked)")
    print("✅ CORS Configuration")
    print("✅ Environment Variables")
    print("✅ Integration Workflows")
    print("✅ Dependency Checking")
    
    # Recommendations
    print("\n" + "="*60)
    print("💡 RECOMMENDATIONS")
    print("="*60)
    
    if total_failures == 0 and total_errors == 0:
        print("🎉 All tests passed! Your Flask application is well-tested.")
        print("📝 Consider adding more edge cases as your application grows.")
        print("🔄 Run tests regularly during development.")
    else:
        print("🔧 Some tests failed. Review the failures above.")
        print("🐛 Fix failing tests before deploying to production.")
        print("📋 Consider adding more comprehensive error handling.")
    
    print("\n📚 To run individual test suites:")
    print("   python3 test_basic.py")
    print("   python3 test_flask_startup_fixed.py")
    
    # Return appropriate exit code
    if total_failures == 0 and total_errors == 0:
        print("\n🎊 SUCCESS: All tests passed!")
        return 0
    else:
        print(f"\n💥 FAILURE: {total_failures + total_errors} test(s) failed!")
        return 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
