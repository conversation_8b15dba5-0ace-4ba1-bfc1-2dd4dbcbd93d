import os
JIRA_TOKEN = os.getenv("JIRA_TOKEN")
JIRA_URL = "https://jira-dc.paloaltonetworks.com/"
JIRA_PROJECT_KEY = "CRTX"
JIRA_ISSUE_TYPE_NAME = "Task"
from atlassian import Jira
    
jira = Jira(token=JIRA_TOKEN, url=JIRA_URL)
# print(meta.body)


# Create a Jira connection using the JIRA library
# headers = JIRA.DEFAULT_OPTIONS["headers"].copy()
# headers["Authorization"] = f"Bearer {pat}"
# jira = JIRA(server=host, options={"headers": headers})

# Issue data (replace with your own data)
issue_data = {
    "project": {"key": "CRTX"},
    "assignee": {"name": "rnatour"},
    "summary": "New issue created via Self Service Automation",
    "description": "This is a sample issue created via WhiltelistingSelf Service Automation.",
    "issuetype": {"name": "Task"},
    "components": [{"name": "DevOps SRE and Monitoring"}]
}

try:
    # Create the issue
    new_issue = jira.create_issue(fields=issue_data)

    # Print the key of the created issue
    print("Issue created successfully!")
    print("Issue Key:", new_issue)
except Exception as e:
    print("Failed to create issue:", str(e))

