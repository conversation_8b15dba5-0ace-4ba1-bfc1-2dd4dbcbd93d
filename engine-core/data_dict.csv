Metric_Name,Recording_Rule,Blacklist_Exists
{'active_cloud_connectors'},{True},{True}
{'active_cloud_outposts'},{True},{True}
{'agent_to_group_calculation_count'},{True},{True}
{'agent_to_group_calculation_duration'},{True},{True}
{'alert_monitoring_metrics_bigquery_alert_status_counts'},{False},{False}
{'alert_monitoring_metrics_mysql_alert_status_counts'},{False},{False}
{'alert_monitoring_metrics_close_incident_with_open_alert_counts'},{False},{False}
{'alert_monitoring_metrics_open_alerts_per_incident_counts'},{False},{False}
{'alert_monitoring_metrics_alert_internal_ids_per_external_id'},{False},{False}
{'alert_monitoring_metrics_mysql_incident_status_counts'},{False},{False}
{'alert_monitoring_metrics_bigquery_incident_status_counts'},{False},{False}
{'alert_monitoring_metrics_duplicate_alert_counts'},{False},{False}
{'alert_monitoring_metrics_incident_alert_count_mismatch'},{False},{False}
{'alert_monitoring_metrics_incident_with_no_alert_counts'},{False},{False}
{'alert_monitoring_metrics_open_alert_older_than_10_days_counts'},{False},{False}
{'analytics__count_events_that_were_fetched_from_bq_total'},{True},{True}
{'analytics__count_events_that_were_fetched_from_gcs_total'},{True},{True}
{'analytics__tenant_have_reached_enrichment_limit'},{False},{False}
{'analytics_active_hosts'},{False},{False}
{'analytics_content_delta_between_requested_sync_to_sync_finished'},{False},{False}
{'analytics_content_delta_time_from_insertion'},{True},{False}
{'analytics_correlations_.+'},{False},{False}
{'analytics_cycle_running'},{False},{False}
{'analytics_de_v2__batches_processed_wall_time_count'},{True},{False}
{'analytics_de_v2__batches_processed_wall_time_sum'},{True},{False}
{'analytics_de_v2__events_processed_count'},{True},{False}
{'analytics_de_v2__events_processed_per_type_sum'},{True},{False}
{'analytics_de_v2__events_processed_sum'},{True},{False}
{'analytics_de_v2_detection_component_wall_time_sum'},{True},{False}
{'analytics_de_v2_detection_processing_part_wall_time_sum'},{True},{False}
{'analytics_de_v2_internal_queue_size'},{True},{False}
{'analytics_de_v2_pubsub_events_uploaded_to_gcs_events_count_total'},{True},{True}
{'analytics_de_v2_pubsub_events_uploaded_to_gcs_files_count_total'},{True},{True}
{'analytics_de_v2_profile_engine_api_keys_count_sum'},{True},{False}
{'analytics_de_v2_profile_engine_api_request_time_sum'},{True},{False}
{'analytics_de_v2_rocks_keys_count_count'},{True},{False}
{'analytics_de_v2_rocks_keys_count_sum'},{True},{False}
{'analytics_de_v2_rocks_request_time_count'},{True},{False}
{'analytics_de_v2_rocks_request_time_sum'},{True},{False}
{'analytics_de_v2_vectorized_matcher_compile_detectors_sum'},{True},{False}
{'analytics_de_v2_vectorized_matcher_detector_count_per_layer_total'},{True},{True}
{'analytics_de_v2_vectorized_matcher_layer_wall_time_sum'},{True},{False}
{'analytics_de_v2_vectorized_matcher_profiles_api_cache_successful_load_total'},{True},{True}
{'analytics_de_v2_vectorized_matcher_udf_process_time_count'},{True},{True}
{'analytics_de_v2_vectorized_matcher_udf_process_time_sum'},{True},{True}
{'analytics_de_v2_vectorized_matcher_wall_time_sum'},{True},{False}
{'analytics_decay_time_per_staging_table_count'},{False},{False}
{'analytics_decay_time_per_staging_table_sum'},{False},{False}
{'analytics_decision_time_per_staging_table_count'},{False},{False}
{'analytics_decision_time_per_staging_table_sum'},{False},{False}
{'analytics_delta_time_from_last_successful_decider_calculation'},{True},{False}
{'analytics_detection_component_process_time_count'},{True},{False}
{'analytics_detection_component_process_time_sum'},{True},{False}
{'analytics_detection_emitted_alerts_count'},{True},{False}
{'analytics_detection_emitted_alerts_sum'},{True},{False}
{'analytics_detection_engine_consumer_nacks_total'},{True},{False}
{'analytics_detection_hit_publish_time_count'},{True},{False}
{'analytics_detection_hit_publish_time_sum'},{True},{False}
{'analytics_detection_num_of_analytics_product_access_total'},{True},{False}
{'analytics_detection_outer_udf_execution_time_count'},{True},{False}
{'analytics_detection_outer_udf_execution_time_sum'},{True},{False}
{'analytics_detection_profile_matcher_get_profile_from_db_time_count'},{True},{False}
{'analytics_detection_profile_matcher_get_profile_from_db_time_sum'},{True},{False}
{'analytics_detection_state_populator_ingestion_rows_total'},{True},{False}
{'analytics_detection_state_populator_ingestion_time_count'},{True},{False}
{'analytics_detection_state_populator_ingestion_time_sum'},{True},{False}
{'analytics_detection_udf_execution_time_count'},{True},{False}
{'analytics_detection_udf_execution_time_sum'},{True},{False}
{'analytics_de_v2_vectorized_matcher_export_detectors_start_unix_time'},{True},{True}
{'analytics_de_v2_vectorized_matcher_export_detectors_success_unix_time'},{True},{True}
{'analytics_dss_last_updated_on_last_change'},{True},{False}
{'analytics_dss_sync_times_difference'},{True},{False}
{'analytics_dynamic_profile_updater_time_count'},{True},{False}
{'analytics_dynamic_profile_updater_time_sum'},{True},{False}
{'analytics_enabled'},{False},{False}
{'analytics_flood_stuck_alerts_total'},{True},{False}
{'analytics_flood_tasks_total'},{True},{False}
{'analytics_global_flood_total'},{True},{False}
{'analytics_global_profiles_contribution_failure'},{False},{False}
{'analytics_local_flood_total'},{False},{False}
{'analytics_objects_table_last_creation_time'},{False},{False}
{'analytics_product_consecutive_stage_failures'},{True},{True}
{'analytics_product_last_successful_api_table_creation_timestamp'},{True},{True}
{'analytics_profile_.+'},{False},{False}
{'analytics_rocksdb_num_keys_query'},{False},{False}
{'analytics_rocksdb_num_requests'},{False},{False}
{'analytics_rocksdb_response_time_count'},{False},{False}
{'analytics_rocksdb_response_time_sum'},{False},{False}
{'analytics_rocksdb_backup_download_failed'},{False},{False}
{'analytics_rocksdb_compaction_tasks'},{False},{False}
{'analytics_role_calculation_delay'},{False},{False}
{'analytics_role_count'},{False},{False}
{'apisec_asset_manager_asset_manager_delete_assets_total_errors'},{True},{False}
{'apisec_asset_manager_asset_manager_get_assets_retention_total_errors'},{True},{False}
{'apisec_asset_manager_asset_manager_get_assets_total_errors'},{True},{False}
{'apisec_asset_manager_asset_manager_publish_assets_total_errors'},{True},{False}
{'apisec_asset_manager_assets_batch_size'},{False},{False}
{'apisec_asset_manager_assets_retention_flow_duration_seconds_bucket'},{True},{True}
{'apisec_asset_manager_assets_retention_flow_duration_seconds_sum'},{True},{True}
{'apisec_asset_manager_assets_retention_flow_duration_seconds_count'},{True},{True}
{'apisec_asset_manager_delete_asset_messages_total'},{True},{True}
{'apisec_asset_manager_get_assets_status_code'},{True},{True}
{'apisec_asset_manager_process_specs_counter'},{False},{False}
{'apisec_asset_manager_publish_uai_flow_duration_seconds_bucket'},{True},{True}
{'apisec_asset_manager_publish_uai_flow_duration_seconds_count'},{True},{True}
{'apisec_asset_manager_publish_uai_flow_duration_seconds_sum'},{True},{True}
{'apisec_asset_manager_pubsub_input_total_errors'},{True},{True}
{'apisec_asset_manager_total_assets_ETL_timeouts'},{True},{True}
{'apisec_asset_manager_total_delete_asset_messages'},{False},{False}
{'apisec_asset_manager_total_number_of_input_DTOs'},{False},{False}
{'apisec_asset_manager_total_number_of_pubsub_input_messages'},{True},{True}
{'apisec_asset_manager_total_publish_asset_messages'},{True},{True}
{'apisec_asset_manager_total_retries_for_get_assets_api_call'},{False},{False}
{'apisec_asset_manager_total_UAI_asset_ingestion_errors'},{True},{True}
{'apisec_asset_manager_upsert_asset_flow_duration_seconds_bucket'},{True},{True}
{'apisec_asset_manager_upsert_asset_flow_duration_seconds_count'},{True},{True}
{'apisec_asset_manager_upsert_asset_flow_duration_seconds_sum'},{True},{True}
{'apisec_bff_api_concurrent_requests'},{False},{False}
{'apisec_bff_api_latency_seconds_bucket'},{True},{True}
{'apisec_bff_api_request_size_bytes_bucket'},{True},{True}
{'apisec_bff_api_requests_total'},{False},{False}
{'apisec_bff_api_response_size_bytes_bucket'},{True},{True}
{'apisec_enricher_apigw_handle_metablob_time_ms'},{True},{True}
{'apisec_enricher_apigw_http_transactions_creation_count'},{False},{False}
{'apisec_enricher_apigw_http_transactions_creation_error_count'},{True},{True}
{'apisec_enricher_apigw_http_transactions_creation_error_on_second_time_count'},{False},{False}
{'apisec_enricher_apigw_http_transactions_creation_on_second_time_count'},{False},{False}
{'apisec_enricher_find_geo_ip_location_time_ms'},{True},{True}
{'apisec_enricher_http_handler_handle_metablob_time_ms'},{True},{True}
{'apisec_enricher_metablob_handler_not_found_count'},{True},{True}
{'apisec_enricher_metablob_parsing_errors_count'},{True},{True}
{'apisec_enricher_parsing_rules_raw_data_into_dto_errors_count'},{False},{False}
{'apisec_enricher_pubsub_callback_time_ms'},{False},{False}
{'apisec_enricher_received_pubsub_msgs_count'},{False},{False}
{'apisec_enricher_send_http_transactions_to_groupping_pubsub_count'},{False},{False}
{'apisec_enricher_set_module_sources_errors_count'},{False},{False}
{'apisec_grouping_service_api_model_collapse_per_depth_per_height'},{False},{False}
{'apisec_grouping_service_api_tree_check_tree_logic_duration_bucket'},{True},{True}
{'apisec_grouping_service_api_tree_check_tree_logic_duration_count'},{True},{True}
{'apisec_grouping_service_api_tree_check_tree_logic_duration_sum'},{True},{True}
{'apisec_grouping_service_api_tree_number_of_trees_per_height'},{False},{False}
{'apisec_grouping_service_mongo_operation_latency_seconds_bucket'},{True},{True}
{'apisec_grouping_service_mongo_operation_latency_seconds_count'},{True},{True}
{'apisec_grouping_service_mongo_operation_latency_seconds_sum'},{True},{True}
{'apisec_grouping_service_number_of_filtered_paths_by_filter_type'},{False},{False}
{'apisec_grouping_service_number_of_paths_per_onboarding_status'},{False},{False}
{'apisec_grouping_service_pubsub_input_counter'},{True},{True}
{'apisec_grouping_service_pubsub_input_error_counter'},{True},{True}
{'apisec_grouping_service_pubsub_output_counter'},{True},{True}
{'apisec_grouping_service_pubsub_output_error_counter'},{True},{True}
{'apisec_grouping_service_regex_check_regex_duration_bucket'},{True},{True}
{'apisec_grouping_service_regex_check_regex_duration_count'},{True},{True}
{'apisec_grouping_service_regex_check_regex_duration_sum'},{True},{True}
{'apisec_grouping_service_regex_hits_total'},{False},{False}
{'apisec_grouping_service_single_transaction_flow_duration_bucket'},{True},{True}
{'apisec_grouping_service_single_transaction_flow_duration_count'},{True},{True}
{'apisec_grouping_service_single_transaction_flow_duration_sum'},{True},{True}
{'apisec_inspection_api_aggregation_counter'},{False},{False}
{'apisec_inspection_api_aggregation_duration_bucket'},{True},{True}
{'apisec_inspection_api_aggregation_duration_count'},{True},{True}
{'apisec_inspection_api_aggregation_duration_sum'},{True},{True}
{'apisec_inspection_api_lru_init_counter'},{False},{False}
{'apisec_inspection_bit_aggregation_counter'},{False},{False}
{'apisec_inspection_content_error'},{False},{False}
{'apisec_inspection_content_pull'},{False},{False}
{'apisec_inspection_content_pull_duration_bucket'},{True},{True}
{'apisec_inspection_content_pull_duration_count'},{True},{False}
{'apisec_inspection_content_pull_duration_sum'},{True},{False}
{'apisec_inspection_content_update_check'},{True},{True}
{'apisec_inspection_content_update_check_duration_bucket'},{True},{True}
{'apisec_inspection_content_update_check_duration_count'},{True},{True}
{'apisec_inspection_content_update_check_duration_sum'},{True},{True}
{'apisec_inspection_content_update_check_error'},{True},{True}
{'apisec_inspection_issue_count'},{False},{False}
{'apisec_inspection_issue_creation_error_count'},{True},{True}
{'apisec_inspection_lru_entry_total_counter'},{False},{False}
{'apisec_inspection_lru_error'},{True},{True}
{'apisec_inspection_malformed_content'},{False},{False}
{'apisec_inspection_message_processing_duration_bucket'},{True},{True}
{'apisec_inspection_message_processing_duration_count'},{True},{True}
{'apisec_inspection_message_processing_duration_sum'},{True},{True}
{'apisec_inspection_pubsub_input_counter'},{True},{True}
{'apisec_inspection_pubsub_input_error_counter'},{True},{True}
{'apisec_inspection_pubsub_output_counter'},{False},{False}
{'apisec_inspection_pubsub_output_error_counter'},{True},{True}
{'apisec_inspection_rule_engine_aggregation_cache_eviction'},{False},{False}
{'apisec_inspection_rule_engine_cycle'},{False},{False}
{'apisec_inspection_rule_engine_execution'},{False},{False}
{'apisec_inspection_rule_error'},{True},{True}
{'apisec_inspection_rule_evaluation_duration_bucket'},{True},{True}
{'apisec_inspection_rule_execution_duration_bucket'},{True},{True}
{'apisec_inspection_telemetry_error_counter'},{True},{True}
{'apisec_inspection_telemetry_sent_counter'},{False},{False}
{'apisec_inspection_telemetry_throttled_counter'},{False},{False}
{'apisec_inspection_unknown_content'},{False},{False}
{'apisec_risk_engine_content_errors_total'},{False},{False}
{'apisec_risk_engine_drift_detection_duration_seconds_bucket'},{True},{True}
{'apisec_risk_engine_drift_detection_duration_seconds_count'},{False},{False}
{'apisec_risk_engine_drift_detection_duration_seconds_sum'},{False},{False}
{'apisec_risk_engine_get_speclets_calls_total'},{False},{False}
{'apisec_risk_engine_get_speclets_duration_seconds_bucket'},{True},{True}
{'apisec_risk_engine_get_speclets_duration_seconds_count'},{False},{False}
{'apisec_risk_engine_get_speclets_duration_seconds_sum'},{False},{False}
{'apisec_risk_engine_get_speclets_invalid_total'},{False},{False}
{'apisec_risk_engine_get_spec_calls_total'},{False},{False}
{'apisec_risk_engine_get_spec_duration_seconds_bucket'},{True},{True}
{'apisec_risk_engine_get_spec_duration_seconds_count'},{False},{False}
{'apisec_risk_engine_get_spec_duration_seconds_sum'},{False},{False}
{'apisec_risk_engine_findings_upsert_duration_seconds_bucket'},{True},{True}
{'apisec_risk_engine_findings_upsert_duration_seconds_count'},{True},{True}
{'apisec_risk_engine_findings_upsert_duration_seconds_sum'},{True},{True}
{'apisec_risk_engine_findings_upsert_emits'},{False},{False}
{'apisec_risk_engine_findings_upserted'},{False},{False}
{'apisec_risk_engine_issues_upsert_duration_seconds_bucket'},{True},{True}
{'apisec_risk_engine_issues_upsert_duration_seconds_count'},{True},{True}
{'apisec_risk_engine_issues_upsert_duration_seconds_sum'},{True},{True}
{'apisec_risk_engine_issues_upsert_emits'},{False},{False}
{'apisec_risk_engine_issues_upserted'},{False},{False}
{'apisec_risk_engine_metablobs_handled'},{False},{False}
{'apisec_risk_engine_specs_handled_total'},{False},{False}
{'apisec_risk_engine_spec_cache_access_total'},{False},{False}
{'apisec_risk_engine_spec_risks_detection_duration_seconds_bucket'},{False},{False}
{'apisec_risk_engine_spec_risks_detection_duration_seconds_count'},{False},{False}
{'apisec_risk_engine_spec_risks_detection_duration_seconds_sum'},{False},{False}
{'apisec_risk_engine_spec_static_scan_failures_total'},{False},{False}
{'apisec_risk_engine_transaction_handling_duration_in_seconds_bucket'},{True},{True}
{'apisec_risk_engine_transaction_handling_duration_in_seconds_count'},{True},{True}
{'apisec_risk_engine_transaction_handling_duration_in_seconds_sum'},{True},{True}
{'apisec_risk_engine_transactions_handled'},{False},{False}
{'apisec_risk_engine_transactions_risk_detection_duration_in_seconds_bucket'},{True},{True}
{'apisec_risk_engine_transactions_risk_detection_duration_in_seconds_count'},{True},{True}
{'apisec_risk_engine_transactions_risk_detection_duration_in_seconds_sum'},{True},{True}
{'apisec_scan_manager_upload_scan_results_error_total'},{False},{False}
{'apisec_scan_manager_upload_scan_results_count_total'},{False},{False}
{'apisec_spec_service_errors_total'},{True},{True}
{'apisec_spec_service_mongo_duration_seconds_bucket'},{True},{True}
{'apisec_spec_service_mongo_duration_seconds_count'},{False},{False}
{'apisec_spec_service_mongo_duration_seconds_sum'},{False},{False}
{'apisec_spec_service_pubsub_duration_seconds_bucket'},{True},{True}
{'apisec_spec_service_pubsub_duration_seconds_count'},{False},{False}
{'apisec_spec_service_pubsub_duration_seconds_sum'},{False},{False}
{'app_hub_prometheus_ingester_edr_errors_total'},{True},{True}
{'app_hub_prometheus_ingester_xql_errors_total'},{True},{True}
{'application_hub_permanent_errors_total'},{True},{True}
{'application_hub_public_api_requests_total'},{True},{True}
{'application_hub_temporary_errors_total'},{True},{True}
{'asm_alerts_mitre_backfill_sync_error_total'},{True},{True}
{'asm_etl_complete'},{False},{False}
{'asm_etl_count'},{False},{False}
{'asm_etl_duration'},{False},{False}
{'asm_export_assets_gauge'},{True},{True}
{'asm_incidents_mitre_backfill_sync_error_total'},{True},{True}
{'asm_mitre_mappings_sync_error_total'},{True},{True}
{'asset_mgmt_assoc_engine_acquire_lock_count_total'},{True},{True}
{'asset_mgmt_assoc_engine_association_conflicts_count'},{False},{False}
{'asset_mgmt_assoc_engine_association_process_time_count'},{True},{True}
{'asset_mgmt_assoc_engine_association_process_time_sum'},{True},{True}
{'asset_mgmt_diff_maker_last_exec_time_sec'},{True},{True}
{'asset_mgmt_general_assets_count_per_cloud_provider'},{True},{True}
{'asset_mgmt_general_assets_count_per_source'},{True},{True}
{'asset_mgmt_general_total_assets_count'},{True},{True}
{'asset_mgmt_ingester_assets_processed_count_total'},{True},{True}
{'asset_mgmt_reducer_last_exec_time_sec'},{True},{True}
{'asset_mgmt_snapshot_mgr_acquire_lock_total'},{True},{True}
{'archive_storage_aggregator_aggregator_compression_rate'},{True},{True}
{'archive_storage_aggregator_aggregator_process_duration'},{True},{True}
{'archive_storage_aggregator_committed_object_size'},{True},{True}
{'archive_storage_aggregator_compression_job_status'},{True},{True}
{'archive_storage_aggregator_delete_objects_count'},{True},{True}
{'archive_storage_aggregator_parse_error_count'},{True},{True}
{'archive_storage_aggregator_process_object_duration_micro_seconds_bucket'},{True},{True}
{'archive_storage_aggregator_processed_bytes_total'},{True},{True}
{'archive_storage_aggregator_processed_objects_count'},{True},{True}
{'archive_storage_aggregator_raw_object_size'},{True},{True}
{'argo_workflows_.+'},{False},{False}
{'attack_path_rules'},{True},{True}
{'attack_path_start_total'},{True},{True}
{'attack_path_success_total'},{True},{True}
{'attack_path_verdicts'},{True},{True}
{'attack_path_failure'},{True},{True}
{'auto_suggest_failure_total'},{True},{True}
{'batch_scanner_assets'},{True},{True}
{'batch_scanner_assets_scanned_milliseconds_bucket'},{True},{True}
{'batch_scanner_assets_scanned_milliseconds_count'},{False},{False}
{'batch_scanner_assets_scanned_milliseconds_sum'},{False},{False}
{'batch_scanner_rules_processed_total'},{True},{True}
{'batch_scanner_verdict_generated'},{True},{True}
{'bigquery_adapter_.+'},{False},{False}
{'blast_radius_crown_jewel_counter_total'},{True},{True}
{'blast_radius_path_counter_total'},{True},{True}
{'blast_radius_paths_by_length_total'},{True},{True}
{'blast_radius_service_time_seconds'},{True},{True}
{'blast_radius_source_counter_total'},{True},{True}
{'cas_dashboards_api_service_duration_seconds_bucket'},{True},{True}
{'cas_dashboards_api_service_duration_seconds_count'},{True},{True}
{'cas_dashboards_api_service_duration_seconds_sum'},{True},{True}
{'ciem_counter_collection_list_accounts_api_results_counter_total'},{False},{False}
{'ciem_counter_epc_validation_snapshot_failure_counter_total'},{True},{True}
{'ciem_counter_epc_sync_page_upload_failure_counter_total'},{True},{True}
{'ciem_counter_sync_sync_publish_accounts_counter_total'},{False},{False}
{'ciem_counter_last_access_processor_added_records_counter_total'},{False},{False}
{'ciem_counter_last_access_audit_log_transformer_processed_records_counter'},{False},{False}
{'ciem_counter_last_access_audit_log_transformer_processed_records_counter_total'},{False},{False}
{'ciem_counter_last_access_audit_log_transformer_unrecognized_records_counter_total'},{False},{False}
{'ciem_counter_last_access_processor_compacted_records_counter_total'},{False},{False}
{'ciem_counter_last_access_processor_merged_supported_actions_counter_total'},{False},{False}
{'ciem_gauge_collection_list_accounts_api_results_gauge'},{False},{False}
{'ciem_gauge_creation_time_slot_time_sec'},{False},{False}
{'ciem_gauge_creation_time_total_bytes_processed'},{False},{False}
{'ciem_gauge_epc_sync_permissions_calculated_gauge'},{True},{True}
{'ciem_gauge_epc_validation_snapshot_to_big_query_time'},{False},{False}
{'ciem_gauge_issues_publisher_total_published'},{False},{False}
{'ciem_gauge_last_access_processor_supported_actions_gauge'},{False},{False}
{'ciem_gauge_rule_scanner_orchestrator_success_percentages'},{False},{False}
{'ciem_gauge_rule_scanner_orchestrator_total_rules'},{False},{False}
{'ciem_timer_account_organization_syncer_get_configs_time_seconds_max'},{True},{True}
{'ciem_timer_account_organization_syncer_get_configs_time_seconds_count'},{False},{False}
{'ciem_timer_account_organization_syncer_get_configs_time_seconds_sum'},{False},{False}
{'ciem_timer_attributes_calculator_job_category_elapsed_time_seconds_count'},{False},{False}
{'ciem_timer_attributes_calculator_job_category_elapsed_time_seconds_sum'},{False},{False}
{'ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_max'},{True},{True}
{'ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_count'},{False},{False}
{'ciem_timer_cortex_account_organization_syncer_calculate_epc_time_seconds_sum'},{False},{False}
{'ciem_timer_collection_list_accounts_api_time_seconds_count'},{False},{False}
{'ciem_timer_creation_time_maintenance_job_elapsed_time_seconds_max'},{False},{False}
{'ciem_timer_epc_consumer_total_time_seconds_max'},{True},{True}
{'ciem_timer_epc_consumer_total_time_seconds_count'},{True},{False}
{'ciem_timer_epc_consumer_total_time_seconds_sum'},{False},{False}
{'ciem_timer_epc_sync_total_sync_time_seconds_count'},{True},{True}
{'ciem_timer_epc_sync_total_sync_time_seconds_sum'},{True},{False}
{'ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_count'},{False},{False}
{'ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_max'},{False},{False}
{'ciem_timer_excessive_controller_calculate_policy_excessive_statements_seconds_sum'},{False},{False}
{'ciem_timer_graph_controller_calculate_graph_seconds_count'},{False},{False}
{'ciem_timer_graph_controller_calculate_graph_seconds_max'},{False},{False}
{'ciem_timer_graph_controller_calculate_graph_seconds_sum'},{False},{False}
{'ciem_timer_last_access_processor_fetch_page_elapsed_time_seconds_count'},{False},{False}
{'ciem_timer_last_access_processor_fetch_page_elapsed_time_seconds_max'},{False},{False}
{'ciem_timer_last_access_audit_log_transformer_elapsed_time_seconds_max'},{False},{False}
{'ciem_timer_last_access_audit_log_transformer_elapsed_time_seconds_count'},{False},{False}
{'ciem_timer_last_access_audit_log_transformer_elapsed_time_seconds_sum'},{False},{False}
{'ciem_timer_last_access_processor_insert_elapsed_time_seconds_sum'},{False},{False}
{'ciem_timer_last_access_processor_insert_elapsed_time_seconds_count'},{False},{False}
{'ciem_timer_last_access_processor_fetch_page_elapsed_time_seconds_sum'},{False},{False}
{'ciem_timer_last_access_processor_insert_elapsed_time_seconds_max'},{False},{False}
{'ciem_timer_last_access_processor_job_elapsed_time_seconds_max'},{False},{False}
{'ciem_timer_rule_management_get_enabled_rules_seconds_count'},{False},{False}
{'ciem_timer_rule_management_get_enabled_rules_seconds_max'},{False},{False}
{'ciem_timer_rule_management_get_enabled_rules_seconds_sum'},{False},{False}
{'ciem_timer_rule_scanner_orchestrator_run_seconds_sum'},{False},{False}
{'ciem_timer_rule_scanner_orchestrator_run_seconds_count'},{False},{False}
{'ciem_timer_rule_scanner_orchestrator_run_seconds_max'},{False},{False}
{'ciem_timer_rule_scanner_run_seconds_count'},{False},{False}
{'ciem_timer_rule_scanner_run_seconds_max'},{False},{False}
{'ciem_timer_rule_scanner_run_seconds_sum'},{False},{False}
{'ciem_timer_sync_total_time_seconds_count'},{True},{False}
{'ciem_timer_sync_total_time_seconds_max'},{False},{False}
{'ciem_timer_sync_total_time_seconds_sum'},{True},{False}
{'ciem_timer_table_controller_get_data_seconds_count'},{False},{False}
{'ciem_timer_table_controller_get_data_seconds_max'},{False},{False}
{'ciem_timer_table_controller_get_data_seconds_sum'},{False},{False}
{'ciem_timer_table_controller_get_view_def_seconds_count'},{False},{False}
{'ciem_timer_table_controller_get_view_def_seconds_max'},{False},{False}
{'ciem_timer_table_controller_get_view_def_seconds_sum'},{False},{False}
{'cloud_connectors_templates_created_total'},{True},{True}
{'cloud_onboarding_errors_total'},{True},{True}
{'cloud_outposts_templates_created_total'},{True},{True}
{'cloud_assets_collection_csp_api_request_duration_seconds_bucket'},{True},{False}
{'cloud_assets_collection_csp_api_request_duration_seconds_count'},{True},{False}
{'cloud_assets_collection_csp_api_request_duration_seconds_sum'},{True},{False}
{'cloud_assets_collection_num_failed_tasks_total'},{True},{True}
{'cloud_assets_collection_num_successful_tasks_total'},{True},{True}
{'cloud_assets_collection_num_content_archive_files_download_failures_total'},{False},{False}
{'cloud_assets_collection_num_rit_files_yaml_parsing_failure_total'},{False},{False}
{'cloud_assets_collection_num_rits_content_version_failures_total'},{False},{False}
{'cloud_assets_collection_num_wrong_format_content_files_total'},{False},{False}
{'cloud_assets_collection_message_processing_duration_seconds_bucket'},{True},{True}
{'cloud_assets_collection_message_processing_duration_seconds_count'},{False},{False}
{'cloud_assets_collection_message_processing_duration_seconds_sum'},{False},{False}
{'cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_bucket'},{True},{False}
{'cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_count'},{False},{False}
{'cloud_assets_collection_message_publish_to_processing_complete_duration_seconds_sum'},{False},{False}
{'cloud_assets_collection_message_publish_to_processing_start_duration_seconds_bucket'},{True},{False}
{'cloud_assets_collection_message_publish_to_processing_start_duration_seconds_count'},{False},{False}
{'cloud_assets_collection_message_publish_to_processing_start_duration_seconds_sum'},{False},{False}
{'cloud_assets_collection_platform_api_request_duration_seconds_bucket'},{True},{False}
{'cloud_assets_collection_platform_api_request_duration_seconds_count'},{False},{False}
{'cloud_assets_collection_platform_api_request_duration_seconds_sum'},{False},{False}
{'cloud_assets_collection_rit_files_duplications'},{False},{False}
{'cc_cache_update_time_seconds_bucket'},{True},{True}
{'classification_mgmt_bigquery_write_error_total'},{True},{True}
{'classification_mgmt_pubsub_publish_error_total'},{True},{True}
{'classification_mgmt_content_delivery_error_total'},{True},{True}
{'classification_mgmt_dashboard_error_total'},{True},{True}
{'cns_asset_conversion_errors_total'},{True},{True}
{'cns_engine_errors_total'},{False},{False}
{'cns_invalid_policies'},{True},{True}
{'cns_invalid_rules'},{True},{True}
{'cns_issues_emitter_duration_seconds_count'},{True},{True}
{'cns_issues_emitter_errors_total'},{False},{False}
{'cns_issues_emitter_queue_size'},{False},{False}
{'cns_job_duration_seconds'},{False},{False}
{'cns_job_init_failed'},{False},{False}
{'cns_num_assets'},{False},{False}
{'cns_num_policies'},{False},{False}
{'cns_num_rules'},{False},{False}
{'cns_policy_issues_generated'},{False},{False}
{'cns_rule_duration_seconds'},{False},{False}
{'cns_rule_findings_generated'},{False},{False}
{'cold_storage_aggregator_.+.'},{False},{False}
{'cold_storage_datasets_aggregator_aggregator_compression_rate'},{True},{True}
{'cold_storage_datasets_aggregator_aggregator_process_duration'},{True},{True}
{'cold_storage_datasets_aggregator_committed_object_size'},{True},{True}
{'cold_storage_datasets_aggregator_compression_job_status'},{True},{True}
{'cold_storage_datasets_aggregator_dataset_errors_count'},{True},{True}
{'cold_storage_datasets_aggregator_delete_objects_count'},{True},{True}
{'cold_storage_datasets_aggregator_processed_bytes_total'},{True},{True}
{'cold_storage_datasets_aggregator_processed_objects_count'},{True},{True}
{'cold_storage_datasets_aggregator_raw_object_size'},{True},{True}
{'cold_storage_datasets_aggregator_spawned_aggregators_count'},{True},{True}
{'cold_tables_sync_job_failure_total'},{True},{False}
{'contextual_search_graph_neo4j_query_execution_time_millis_bucket'},{True},{True}
{'contextual_search_graph_neo4j_query_execution_time_millis_count'},{True},{True}
{'contextual_search_graph_neo4j_query_execution_time_millis_created'},{True},{True}
{'contextual_search_graph_neo4j_query_execution_time_millis_sum'},{True},{True}
{'cortex_gw_messages_processor_.+'},{False},{False}
{'cortex_cdl_to_clcs_migration_failed_total'},{True},{False}
{'cortex_cdl_to_clcs_migration_succeeded_total'},{True},{False}
{'cortex_platform_http_request_duration_highr_seconds_count'},{False},{False}
{'cortex_platform_http_request_duration_highr_seconds_sum'},{False},{False}
{'cortex_platform_http_request_duration_seconds_count'},{False},{False}
{'cortex_platform_http_request_duration_seconds_sum'},{False},{False}
{'cortex_platform_http_request_size_bytes_count'},{False},{False}
{'cortex_platform_http_request_size_bytes_sum'},{False},{False}
{'cortex_platform_http_requests_total'},{False},{False}
{'cortex_platform_http_response_size_bytes_count'},{False},{False}
{'cortex_platform_http_response_size_bytes_sum'},{False},{False}
{'cronus_active_connections_total'},{True},{False}
{'cronus_client_client_roundtrip_latency_sec_bucket'},{True},{True}
{'cronus_client_connection_wait_duration_seconds_bucket'},{True},{True}
{'cronus_client_rate_limited_requests_total'},{True},{True}
{'cronus_client_requests_total'},{True},{True}
{'cronus_dao_inserts_total'},{False},{False}
{'cronus_db_repair_dropped_entries_total'},{True},{True}
{'cronus_handler_wait_time_seconds_bucket'},{False},{False}
{'cronus_hotkeys_count_delay_duration_seconds_bucket'},{True},{True}
{'cronus_hotkeys_count_delay_duration_seconds_count'},{False},{True}
{'cronus_hotkeys_count_delay_duration_seconds_sum'},{False},{True}
{'cronus_hotkeys_count_duration_seconds_bucket'},{True},{True}
{'cronus_hotkeys_count_duration_seconds_count'},{False},{True}
{'cronus_hotkeys_count_duration_seconds_sum'},{False},{True}
{'cronus_hotkeys_count_in_time_window'},{False},{False}
{'cronus_hotkeys_events_count_in_time_window'},{False},{False}
{'cronus_hotkeys_threshold_in_time_window'},{False},{False}
{'cronus_log_cache_get_ops'},{True},{False}
{'cronus_operator_cluster_hotkeys_count_in_time_window'},{False},{False}
{'cronus_operator_cluster_hotkeys_events_count_in_time_window'},{False},{False}
{'cronus_rebalance_download_bytes_total'},{True},{True}
{'cronus_rebalance_read_bytes_total'},{True},{True}
{'cronus_rebalance_upload_bytes_total'},{True},{True}
{'cronus_request_duration_seconds_bucket'},{True},{False}
{'cronus_request_process_duration_seconds_bucket'},{True},{False}
{'cronus_requests_queue_stream_feed_wait_duration_seconds_bucket'},{True},{False}
{'cronus_requests_total'},{True},{False}
{'cronus_rows_index_latency_seconds_bucket'},{True},{True}
{'cronus_storage_latency_seconds_bucket'},{False},{False}
{'cronus_throttled_write_requests_total'},{True},{True}
{'cronus_tree_index_compaction_duration_seconds_bucket'},{True},{True}
{'cwp_.*'},{True},{True}
{'cwp_api_latency_seconds_bucket'},{True},{False}
{'cwp_api_latency_seconds_count'},{False},{False}
{'cwp_api_latency_seconds_sum'},{False},{False}
{'cwp_api_requests_total'},{True},{False}
{'cwp_ci_analyzer_api_requests_total'},{False},{False}
{'cwp_malwaredetection_wildfire_latency_seconds_bucket'},{True},{True}
{'cwp_malwaredetection_wildfire_latency_seconds_count'},{False},{True}
{'cwp_malwaredetection_wildfire_latency_seconds_sum'},{False},{True}
{'cwp_sp_snapshot_auto_deleted_total'},{True},{True}
{'cwp_sp_snapshot_csp_requests_total'},{True},{True}
{'cwp_sp_snapshot_dangling_total'},{True},{True}
{'cwp_sp_snapshot_lifetime_seconds_bucket'},{True},{True}
{'cwp_sp_snapshot_lifetime_seconds_count'},{False},{False}
{'cwp_sp_snapshot_lifetime_seconds_sum'},{False},{False}
{'cwp_sp_snapshot_operation_duration_seconds_bucket'},{True},{True}
{'cwp_sp_snapshot_operation_duration_seconds_count'},{False},{False}
{'cwp_sp_snapshot_operation_duration_seconds_sum'},{False},{False}
{'cwp_sp_snapshot_request_avg_waiting_time_seconds'},{False},{False}
{'cwp_sp_snapshot_requests_missed_sla'},{True},{True}
{'dashboard_engine_.+'},{False},{False}
{'data_ingestion_health_ingestion_alerts_total'},{True},{False}
{'dbre_backup_succeeded'},{True},{True}
{'neo4j_dbms_page_cache_hit_ratio'},{False},{False}
{'neo4j_dbms_vm_heap_used'},{False},{False}
{'neo4j_dbms_vm_gc_time_g1_old_generation_total'},{False},{False}
{'neo4j_dbms_vm_gc_time_g1_young_generation_total'},{False},{False}
{'dml_.+'},{False},{False}
{'dp_asset_pipeline_assets_errors'},{True},{True}
{'dp_asset_pipeline_assets_total'},{True},{True}
{'dp_asset_pipeline_metablob_errors'},{True},{True}
{'dp_asset_pipeline_performance_bucket'},{True},{True}
{'dp_asset_pipeline_performance_count'},{True},{True}
{'dp_asset_pipeline_performance_sum'},{True},{True}
{'dp_asset_pipeline_skip_get_deleted_assets'},{True},{True}
{'dms_.+'},{False},{False}
{'dss_enabled'},{False},{False}
{'dss_last_updated_on_last_change'},{False},{False}
{'dss_sync_times_difference'},{False},{False}
{'edr_.+'},{False},{False}
{'effective_ip_range_monitoring_overlapping_rules'},{True},{True}
{'egress_aggregator_aggregator_compression_rate'},{True},{True}
{'egress_aggregator_committed_object_size'},{True},{True}
{'egress_aggregator_compression_job_status'},{True},{True}
{'egress_aggregator_delete_objects_count'},{True},{True}
{'egress_aggregator_processed_bytes_total'},{True},{True}
{'egress_aggregator_processed_objects_count'},{True},{True}
{'egress_aggregator_raw_object_size'},{True},{True}
{'egress_aggregator_spawned_aggregators_count'},{True},{True}
{'email_attachment_missing_file'},{False},{False}
{'email_attachment_pending_decrypt'},{False},{False}
{'email_attachment_pending_wf_submit'},{False},{False}
{'email_attachment_raised_alert'},{False},{False}
{'email_attachment_submitted_to_wf'},{False},{False}
{'email_attachment_unsupported_file_type'},{False},{False}
{'email_relay_attachment_submitted_to_wf_total'},{False},{False}
{'email_relay_attachment_verdict_total'},{True},{False}
{'email_relay_attachments_total'},{False},{False}
{'email_relay_gcs_latency_milliseconds_count'},{False},{False}
{'email_relay_gcs_latency_milliseconds_sum'},{False},{False}
{'email_relay_steps_latency_milliseconds_count'},{False},{False}
{'email_relay_steps_latency_milliseconds_sum'},{False},{False}
{'email_relay_wildfire_error_codes_total'},{False},{False}
{'email_relay_wildfire_latency_milliseconds_count'},{False},{False}
{'email_relay_wildfire_latency_milliseconds_sum'},{False},{False}
{'email_relay_wildfire_unsupported_files_total'},{False},{False}
{'ext_controller_element_duration_seconds_count'},{False},{False}
{'ext_controller_element_duration_seconds_sum'},{False},{False}
{'ext_controller_element_status'},{False},{False}
{'failed_requests_total'},{True},{True}
{'finding_pubsub_message_histogram_bucket'},{False},{False}
{'finding_pubsub_message_histogram_count'},{False},{False}
{'finding_pubsub_message_histogram_sum'},{False},{False}
{'findings_table_fetching_time_seconds_bucket'},{True},{True}
{'findings_table_fetching_time_seconds_created'},{True},{True}
{'findings_table_fetching_time_seconds_sum'},{True},{True}
{'xdr_forensics_sams'},{False},{False}
{'xdr_forensics_hunt_lag_hours'},{False},{False}
{'gcs_notification_failed_subscriptions'},{False},{False}
{'GnzEdrPipeline_.+'},{False},{False}
{'GnzGlobal_pithos_active_streams'},{True},{False}
{'GnzGlobal_pithos_aggregated_bytes_total'},{True},{False}
{'GnzGlobal_pithos_aggregated_objects_total'},{True},{False}
{'GnzGlobal_pithos_aggregation_duration_seconds'},{True},{False}
{'GnzGlobal_pithos_client_stream_commit_latency_seconds_count'},{True},{True}
{'GnzGlobal_pithos_client_stream_commit_latency_seconds_sum'},{True},{True}
{'GnzGlobal_pithos_committed_objects_total'},{True},{False}
{'GnzGlobal_pithos_dataset_aggregators'},{True},{False}
{'GnzGlobal_pithos_streamed_bytes_total'},{True},{False}
{'GnzMbIngester_.+'},{False},{False}
{'GnzStoryBuilder_.+'},{False},{False}
{'go_gc_duration_seconds_sum'},{False},{False}
{'go_goroutines'},{False},{False}
{'go_memstats_alloc_bytes'},{False},{False}
{'go_memstats_alloc_bytes_total'},{False},{False}
{'go_memstats_buck_hash_sys_bytes'},{False},{False}
{'go_memstats_gc_sys_bytes'},{False},{False}
{'go_memstats_heap_alloc_bytes'},{False},{False}
{'go_memstats_heap_idle_bytes'},{False},{False}
{'go_memstats_heap_inuse_bytes'},{False},{False}
{'go_memstats_heap_released_bytes'},{False},{False}
{'go_memstats_heap_sys_bytes'},{False},{False}
{'go_memstats_mcache_inuse_bytes'},{False},{False}
{'go_memstats_mcache_sys_bytes'},{False},{False}
{'go_memstats_mspan_inuse_bytes'},{False},{False}
{'go_memstats_mspan_sys_bytes'},{False},{False}
{'go_memstats_next_gc_bytes'},{False},{False}
{'go_memstats_other_sys_bytes'},{False},{False}
{'go_memstats_stack_inuse_bytes'},{False},{False}
{'go_memstats_stack_sys_bytes'},{False},{False}
{'go_memstats_sys_bytes'},{False},{False}
{'gonzo_server_.+'},{False},{False}
{'hpl_json_.+'},{False},{False}
{'http_requests_total'},{True},{False}
{'http_server_requests_seconds_count'},{True},{False}
{'http_server_duration_milliseconds_bucket'},{True},{True}
{'http_server_duration_milliseconds_count'},{True},{True}
{'http_server_duration_milliseconds_sum'},{True},{True}
{'http_server_request_duration_seconds_bucket'},{True},{True}
{'http_server_request_duration_seconds_count'},{True},{True}
{'http_server_request_duration_seconds_sum'},{True},{False}
{'http_server_response_size_bytes_sum'},{True},{True}
{'http_server_response_size_bytes_count'},{True},{True}
{'ingestion_quota_exceeded'},{True},{False}
{'inline_scanner_asset_change_event_failed_total'},{True},{False}
{'inline_scanner_asset_change_event_ignored_total'},{False},{False}
{'inline_scanner_asset_change_event_received_total'},{True},{False}
{'inline_scanner_asset_change_event_replayed_total'},{False},{False}
{'inline_scanner_assets_finding'},{False},{False}
{'inline_scanner_assets_scanned_total'},{True},{False}
{'inline_scanner_cloud_account_fetched'},{False},{False}
{'inline_scanner_findings_published_failed_total'},{True},{False}
{'inline_scanner_findings_published_replayed_total'},{False},{False}
{'inline_scanner_findings_published_success_total'},{True},{False}
{'itdr_data_pipeline_asset_metadata_error_count'},{True},{True}
{'itdr_data_pipeline_asset_metadata_id_not_found_count'},{False},{False}
{'itdr_data_pipeline_asset_metadata_read_duration_bucket'},{False},{False}
{'itdr_data_pipeline_asset_metadata_read_duration_count'},{False},{False}
{'itdr_data_pipeline_asset_metadata_read_duration_sum'},{False},{False}
{'itdr_data_pipeline_asset_metadata_write_duration_bucket'},{False},{False}
{'itdr_data_pipeline_asset_metadata_write_duration_count'},{False},{False}
{'itdr_data_pipeline_asset_metadata_write_duration_sum'},{False},{False}
{'itdr_data_pipeline_cdc_error_count'},{True},{True}
{'itdr_data_pipeline_cdc_write_count'},{False},{False}
{'itdr_data_pipeline_cdc_write_duration_bucket'},{False},{False}
{'itdr_data_pipeline_cdc_write_duration_count'},{False},{False}
{'itdr_data_pipeline_cdc_write_duration_sum'},{False},{False}
{'itdr_data_pipeline_cie_read_row_error'},{False},{False}
{'itdr_data_pipeline_cie_row_errors_processed'},{True},{True}
{'itdr_data_pipeline_cie_rows_processed'},{True},{True}
{'itdr_data_pipeline_cie_total_rows_process_duration_bucket'},{False},{False}
{'itdr_data_pipeline_cie_total_rows_process_duration_count'},{False},{False}
{'itdr_data_pipeline_cie_total_rows_process_duration_sum'},{False},{False}
{'itdr_data_pipeline_deleted_assets_create_extended_fields_error'},{False},{False}
{'itdr_data_pipeline_deleted_assets_read_row_error'},{False},{False}
{'itdr_data_pipeline_deleted_assets_row_errors_processed'},{False},{False}
{'itdr_data_pipeline_deleted_assets_rows_processed'},{False},{False}
{'itdr_data_pipeline_deleted_assets_total_rows_process_duration_bucket'},{True},{True}
{'itdr_data_pipeline_deleted_assets_total_rows_process_duration_count'},{True},{True}
{'itdr_data_pipeline_deleted_assets_total_rows_process_duration_sum'},{True},{True}
{'itdr_data_pipeline_dss_sync_input_count'},{False},{False}
{'itdr_data_pipeline_dss_sync_input_error_count'},{True},{True}
{'itdr_data_pipeline_pubsub_output_count'},{False},{False}
{'itdr_data_pipeline_pubsub_output_error_count'},{False},{False}
{'itdr_data_pipeline_risk_handler_error_count'},{True},{True}
{'itdr_data_pipeline_risk_handler_read_duration_bucket'},{False},{False}
{'itdr_data_pipeline_risk_handler_read_duration_count'},{False},{False}
{'itdr_data_pipeline_risk_handler_read_duration_sum'},{False},{False}
{'itdr_data_pipeline_uai_asset_ingestion_error_count'},{False},{False}
{'itdr_data_pipeline_uai_asset_patch_count'},{False},{False}
{'itdr_data_pipeline_uai_asset_patch_error_count'},{False},{False}
{'itdr_risk_processor_bigquery_output_counter'},{False},{False}
{'itdr_risk_processor_bigquery_output_error_counter'},{False},{False}
{'itdr_risk_processor_case_update_without_assets_counter'},{False},{False}
{'itdr_risk_processor_dao_access_duration_seconds_bucket'},{True},{True}
{'itdr_risk_processor_dao_access_duration_seconds_count'},{True},{True}
{'itdr_risk_processor_dao_access_duration_seconds_sum'},{True},{True}
{'itdr_risk_processor_db_migration_error_counter'},{False},{False}
{'itdr_risk_processor_pubsub_input_counter'},{False},{False}
{'itdr_risk_processor_pubsub_input_error_counter'},{True},{True}
{'itdr_risk_processor_pubsub_output_counter'},{False},{False}
{'itdr_risk_processor_pubsub_output_error_counter'},{True},{True}
{'itdr_risk_processor_risk_cron_duration_bucket'},{False},{False}
{'itdr_risk_processor_risk_cron_duration_count'},{False},{False}
{'itdr_risk_processor_risk_cron_duration_sum'},{False},{False}
{'known_attachment_malwares'},{False},{False}
{'master_tenant_listener_is_stale'},{False},{False}
{'memsql_counter_bytes_received'},{False},{False}
{'memsql_counter_bytes_sent'},{False},{False}
{'memsql_counter_connections'},{False},{False}
{'memsql_counter_failed_read_queries'},{False},{False}
{'memsql_counter_failed_write_queries'},{False},{False}
{'memsql_counter_successful_read_queries'},{False},{False}
{'memsql_counter_successful_write_queries'},{False},{False}
{'memsql_distributed_partitions_offline'},{False},{False}
{'memsql_distributed_partitions_online'},{False},{False}
{'memsql_distributed_partitions_total'},{False},{False}
{'memsql_status_aborted_connects'},{False},{False}
{'memsql_status_failed_read_queries'},{False},{False}
{'memsql_status_failed_write_queries'},{False},{False}
{'memsql_workload_management_total_queries_cancelled_since_startup'},{False},{False}
{'memsql_workload_management_total_queries_finished_since_startup'},{False},{False}
{'memsql_workload_management_total_queries_started_since_startup'},{False},{False}
{'metrics_aggregator_.+'},{False},{False}
{'metrics_aggregator_last_processed_batch'},{False},{False}
{'number_of_cloud_accounts'},{True},{True}
{'neo4j_.*bolt_connections.*'},{False},{False}
{'neo4j_.*bolt_messages_received.*'},{False},{False}
{'neo4j_.*bolt_messages_started.*'},{False},{False}
{'neo4j_dbms_pool_bolt_total_size'},{False},{False}
{'neo4j_dbms_pool_bolt_total_used'},{False},{False}
{'neo4j_dbms_pool_bolt_used_heap'},{False},{False}
{'neo4j_.*check_point.*'},{False},{False}
{'neo4j_.*cypher_replan_events.*'},{False},{False}
{'neo4j_.*cypher_cache.*'},{False},{False}
{'neo4j_.*pool_transaction.*_total_used'},{False},{False}
{'neo4j_.*pool_transaction.*_used_heap'},{False},{False}
{'neo4j_.*store_size.*'},{False},{False}
{'neo4j_.*transaction_active_read'},{False},{False}
{'neo4j_.*transaction_active_write'},{False},{False}
{'neo4j_.*transaction_committed.*'},{False},{False}
{'neo4j_.*transaction_peak_concurrent.*'},{False},{False}
{'neo4j_.*transaction_rollbacks.*'},{False},{False}
{'neo4j_.*page_cache_hit.*'},{False},{False}
{'neo4j_.*page_cache_page_faults.*'},{False},{False}
{'neo4j_.*page_cache_usage_ratio'},{False},{False}
{'neo4j_.*vm_file_descriptors_count'},{False},{False}
{'neo4j_.*vm_gc_time.*'},{False},{False}
{'neo4j_.*vm_heap_used'},{False},{False}
{'otelcol_.+'},{False},{False}
{'partyzaurus_.+'},{False},{False}
{'process_cpu_seconds_total'},{False},{True}
{'process_max_fds'},{False},{False}
{'process_open_fds'},{False},{True}
{'process_resident_memory_bytes'},{False},{True}
{'process_start_time_seconds'},{False},{True}
{'prom_gonzo_storage_adapter_.+'},{False},{False}
{'pz_.+'},{False},{False}
{'pz_schema_manager_.+'},{False},{False}
{'xsoar_podman_cpu'},{False},{False}
{'xsoar_podman_memory'},{False},{False}
{'xsoar_podman_uptime'},{False},{False}
{'xsoar_rc.+'},{False},{False}
{'risk_prioritization_action_plan_counter_total'},{True},{True}
{'risk_prioritization_action_plan_score_gauge_ratio'},{True},{True}
{'risk_prioritization_issue_score_gauge_ratio'},{True},{True}
{'risk_prioritization_learn_state_check_ratio'},{True},{True}
{'risk_prioritization_model_bq_import_seconds'},{True},{True}
{'risk_prioritization_model_create_date_gauge_seconds'},{True},{True}
{'risk_prioritization_upload_model_seconds'},{True},{True}
{'storybuilder_.+'},{False},{False}
{'support_case_auto_generate_tsf_request_timeout_total'},{True},{True}
{'support_case_failed_auto_generate_tsf_request_total'},{True},{True}
{'support_case_failed_upload_files_total'},{True},{True}
{'support_case_number_of_support_case_successfully_created_total'},{True},{True}
{'support_case_number_of_support_case_failed_to_create_total'},{True},{True}
{'support_case_success_auto_generate_tsf_request_total'},{True},{True}
{'support_case_success_upload_files_total'},{True},{True}
{'storybuilder_filtered_events_total'},{True},{False}
{'temporal_worker_task_slots_available'},{True},{True}
{'temporal_worker_task_slots_used'},{True},{True}
{'uai_aggregated_assets_cdc_delay_seconds'},{True},{True}
{'uai_api_errors_count_total'},{True},{True}
{'uai_api_requests_count_total'},{True},{True}
{'vectorized_matcher_current_content_version_exporting_time_in_seconds'},{True},{True}
{'vectorized_matcher_current_content_version_loading_time_in_seconds'},{True},{True}
{'verdict_manager_batches_completed'},{True},{True}
{'verdict_manager_verdicts_fetched'},{True},{True}
{'verdict_manager_findings_created'},{True},{True}
{'verdict_manager_findings_closed'},{True},{True}
{'verdict_manager_findings_publish_failed'},{True},{True}
{'verdict_manager_findings_publish_success'},{True},{True}
{'verdict_manager_issues_created'},{True},{True}
{'verdict_manager_issues_updated'},{True},{True}
{'verdict_manager_issues_closed'},{True},{True}
{'verdict_manager_issues_batch_publish_success'},{True},{True}
{'verdict_manager_issues_batch_publish_failed'},{True},{True}
{'verdict_manager_reconcile_failed'},{True},{True}
{'vsg_reconcile_time_bucket'},{True},{True}
{'vsg_reconcile_time_count'},{False},{False}
{'vsg_reconcile_time_sum'},{False},{False}
{'vsg_oom_scaler_applied_memory_resources'},{False},{False}
{'vsg_oom_scaler_last_applied_timestamp'},{False},{False}
{'vsg_oom_scaler_last_reconcile_timestamp'},{False},{False}
{'vsg_oom_scaler_pod_request_memory_max'},{False},{False}
{'vsg_oom_scaler_reconcile_time_bucket'},{True},{True}
{'vsg_oom_scaler_reconcile_time_count'},{False},{False}
{'vsg_oom_scaler_reconcile_time_sum'},{False},{False}
{'vsg_oom_scaler_resolve_source_metric_bucket'},{True},{True}
{'vsg_oom_scaler_resolve_source_metric_count'},{False},{False}
{'vsg_oom_scaler_resolve_source_metric_sum'},{False},{False}
{'vsg_oom_scaler_target_memory_resources'},{False},{False}
{'vsg_oom_scaler_under_cooldown_timestamp'},{False},{False}
{'vsg_vertical_scaler_applied_cpu_resources'},{False},{False}
{'vsg_vertical_scaler_applied_memory_resources'},{False},{False}
{'vsg_vertical_scaler_current_step_thresholds'},{False},{False}
{'vsg_vertical_scaler_hpa_invalid_timestamp'},{False},{False}
{'vsg_vertical_scaler_last_applied_timestamp'},{False},{False}
{'vsg_vertical_scaler_last_reconcile_timestamp'},{False},{False}
{'vsg_vertical_scaler_reconcile_time_bucket'},{True},{True}
{'vsg_vertical_scaler_reconcile_time_count'},{False},{False}
{'vsg_vertical_scaler_reconcile_time_sum'},{False},{False}
{'vsg_vertical_scaler_resolve_source_metric_bucket'},{True},{True}
{'vsg_vertical_scaler_resolve_source_metric_count'},{False},{False}
{'vsg_vertical_scaler_resolve_source_metric_sum'},{False},{False}
{'vsg_vertical_scaler_scaled_replicas'},{False},{False}
{'vsg_vertical_scaler_source_metric_result'},{False},{False}
{'vsg_vertical_scaler_target_cpu_resources'},{False},{False}
{'vsg_vertical_scaler_target_memory_resources'},{False},{False}
{'vsg_vertical_scaler_target_replicas'},{False},{False}
{'vsg_vertical_scaler_under_cooldown_timestamp'},{False},{False}
{'vsg_zero_scaler_last_applied_timestamp'},{False},{False}
{'vsg_zero_scaler_last_reconcile_timestamp'},{False},{False}
{'vsg_zero_scaler_manually_downscaled_timestamp'},{False},{False}
{'vsg_zero_scaler_reconcile_time_bucket'},{True},{True}
{'vsg_zero_scaler_reconcile_time_count'},{False},{False}
{'vsg_zero_scaler_reconcile_time_sum'},{False},{False}
{'vsg_zero_scaler_resolve_source_metric_bucket'},{True},{True}
{'vsg_zero_scaler_resolve_source_metric_count'},{False},{False}
{'vsg_zero_scaler_resolve_source_metric_sum'},{False},{False}
{'vsg_zero_scaler_scaled_replicas'},{False},{False}
{'vsg_zero_scaler_source_metric_result'},{False},{False}
{'vsg_zero_scaler_target_replicas'},{False},{False}
{'vsg_zero_scaler_thresholds'},{False},{False}
{'vuln_action_plan_creation_failure_total'},{False},{False}
{'vuln_action_plan_creation_metrics'},{False},{False}
{'vuln_action_plan_recon_failure_total'},{False},{False}
{'vuln_action_plan_recon_metrics'},{False},{False}
{'vxp_bigquery_rows_read_total'},{False},{False}
{'vxp_bigquery_slots_used_total'},{False},{False}
{'vxp_http_server_requests_count'},{True},{True}
{'vxp_http_server_requests_sum'},{True},{True}
{'vxp_job_run_time_count'},{False},{False}
{'vxp_issues_published_total'},{False},{False}
{'vxp_job_run_time_sum'},{False},{False}
{'vxp_policy_sync_actions_total'},{False},{False}
{'vxp_policy_sync_findings_total'},{False},{False}
{'wf_vs_get_verdicts_request_latency_seconds_bucket'},{False},{False}
{'wf_vs_get_verdicts_request_failed_total'},{False},{False}
{'wf_vs_get_verdicts_size'},{False},{False}
{'wf_vs_set_upload_request_failed'},{True},{True}
{'wf_vs_set_upload_request_success'},{True},{True}
{'wlm_monitoring_.+'},{False},{False}
{'xcloud_assets_count'},{False},{False}
{'xcloud_discovery_hours_delay'},{False},{False}
{'xcloud_inventory_strategies_count_by_provider_and_status'},{False},{False}
{'xcloud_onboarding_message'},{False},{False}
{'xcloud_strategies_delay_hours_by_service'},{False},{False}
{'xdm_.+'},{False},{False}
{'xdr_active_lightweight_agents_7d'},{False},{False}
{'xdr_addons_license_status'},{True},{False}
{'xdr_agent.+'},{False},{False}
{'xdr_alert_domain_migration_error'},{False},{False}
{'xdr_alert_source_delay_time'},{True},{True}
{'xdr_alert_sync_tags_databases_error_total'},{True},{True}
{'xdr_alerts_.+'},{False},{False}
{'xdr_alerts_fetcher.+'},{False},{False}
{'xdr_api_errors_total'},{False},{False}
{'xdr_api_waitress_allocated_threads'},{False},{False}
{'xdr_api_waitress_occupied_threads'},{False},{False}
{'xdr_api_waitress_queued_requests'},{False},{False}
{'xdr_asm_mega_join_cache_creation_error_total'},{False},{False}
{'xdr_asm_mega_join_cache_delay'},{False},{False}
{'xdr_asm_xpanse_replication_delay'},{False},{False}
{'xdr_asset_command_.+'},{False},{False}
{'xdr_asset_score_changes_received_created'},{False},{False}
{'xdr_asset_score_changes_received_total'},{False},{False}
{'xdr_asset_score_changes_sent_to_cie_created'},{False},{False}
{'xdr_asset_score_changes_sent_to_cie_total'},{False},{False}
{'xdr_bigquery_bytes_processed_count'},{False},{False}
{'xdr_bigquery_bytes_processed_sum'},{False},{False}
{'xdr_bigquery_execution_time_count'},{False},{False}
{'xdr_bigquery_execution_time_sum'},{False},{False}
{'xdr_bigquery_queue_time_count'},{False},{False}
{'xdr_bigquery_queue_time_sum'},{False},{False}
{'xdr_bigquery_zslots_count'},{False},{False}
{'xdr_bigquery_zslots_sum'},{False},{False}
{'xdr_biocs_assigned_to_profiles_count'},{False},{False}
{'xdr_bq_stats_not_delivered_from_pubsub_total'},{False},{False}
{'xdr_bq_stats_redis_list_length_maxed_total'},{False},{False}
{'xdr_broker_.+'},{False},{False}
{'xdr_calc_.+'},{False},{False}
{'xdr_card_.+'},{False},{False}
{'xdr_case_.+'},{False},{False}
{'xdr_cie_risk_score_errors_created'},{False},{False}
{'xdr_cie_risk_score_errors_total'},{False},{False}
{'xdr_clcs_counter'},{False},{False}
{'xdr_clcs_multi_csp_connected'},{True},{True}
{'xdr_clcs_multi_region_connected_count'},{True},{True}
{'xdr_clcs_multi_region_enabled'},{True},{True}
{'xdr_clcs_multi_salesforce_enabled'},{True},{True}
{'xdr_clcs_subscriptions_data'},{False},{False}
{'xdr_cloud_agents_count'},{False},{False}
{'xdr_cloud_license_count'},{False},{False}
{'xdr_cloud_license_purchased'},{False},{False}
{'xdr_cloud_pro_agents_count'},{False},{False}
{'xdr_cold_storage_oldest_raw_object'},{False},{False}
{'xdr_collection_.+'},{False},{False}
{'xdr_collector_.+'},{False},{False}
{'xdr_count_operational_status_and_os_type'},{False},{False}
{'xdr_count_operational_status_description_and_os_type'},{False},{False}
{'xdr_cts_active_sessions'},{True},{True}
{'xdr_cts_active_tokens'},{True},{True}
{'xdr_cts_cache_hits_total'},{False},{False}
{'xdr_cts_cache_miss_total'},{False},{False}
{'xdr_cts_fresh_token_total'},{False},{False}
{'xdr_cts_request_time_count'},{False},{False}
{'xdr_cts_request_time_sum'},{False},{False}
{'xdr_cts_request_total'},{False},{False}
{'xdr_cts_sts_error_total'},{False},{False}
{'xdr_cts_sts_unauthorized_total'},{False},{False}
{'xdr_cts_waitress_allocated_threads'},{False},{False}
{'xdr_cts_waitress_occupied_threads'},{False},{False}
{'xdr_cts_waitress_queued_requests'},{False},{False}
{'xdr_current_connected_agents'},{False},{False}
{'xdr_current_connected_edr_agents'},{False},{False}
{'xdr_data_usage_24_hours'},{False},{False}
{'xdr_device_control_license'},{False},{False}
{'xdr_dss_.*'},{False},{True}
{'xdr_dumpster_auto_upload_config'},{False},{False}
{'xdr_dumpster_auto_upload_dumps_count'},{False},{False}
{'xdr_edr_active_agents_24h'},{False},{False}
{'xdr_edr_agents_24_hours'},{False},{False}
{'xdr_edr_agents_count'},{False},{False}
{'xdr_edr_license_count'},{True},{False}
{'xdr_edr_license_expiration'},{False},{False}
{'xdr_edr_license_purchased'},{False},{False}
{'xdr_egress_oldest_raw_object'},{True},{True}
{'xdr_email_service.+'},{False},{False}
{'xdr_epp_agents_count'},{False},{False}
{'xdr_epp_license_count'},{False},{False}
{'xdr_epp_license_expiration'},{False},{False}
{'xdr_epp_license_purchased'},{False},{False}
{'xdr_forensics_agents'},{False},{False}
{'xdr_forensics_licenses'},{False},{False}
{'xdr_forensics_tenant'},{False},{False}
{'xdr_get_unused_api_key_ids'},{False},{False}
{'xdr_gvs_.+'},{False},{False}
{'xdr_host_insights_is_enabled'},{False},{False}
{'xdr_incident_alert_data_sync_error_total'},{False},{False}
{'xdr_incidents_by_status_avg'},{False},{False}
{'xdr_incidents_by_status_count'},{False},{False}
{'xdr_informative_btp_alerts'},{False},{False}
{'xdr_init_app_status'},{False},{False}
{'xdr_init_app_took'},{False},{False}
{'xdr_invalidate_user_role_failure'},{True},{True}
{'xdr_investigation_in_progress'},{False},{False}
{'xdr_ios_large_digest_report_total'},{True},{True}
{'xdr_kpi_.+'},{False},{False}
{'xdr_license_fetch_failures'},{True},{False}
{'xdr_logging_.*'},{False},{True}
{'xdr_mail_event_processor_.+'},{False},{False}
{'xdr_mailing_queue_count'},{True},{False}
{'xdr_managed_tenant_monitor_info'},{False},{False}
{'xdr_matching_service_detection_queue_depth'},{True},{False}
{'xdr_mdr_license'},{False},{False}
{'xdr_forensics_migration_status'},{False},{False}
{'xdr_mssp_license'},{False},{False}
{'xdr_mth_license'},{False},{False}
{'xdr_mysql_.*'},{True},{True}
{'xdr_nfr_license'},{False},{False}
{'xdr_notifcation_mail_queue_count'},{True},{False}
{'xdr_notification_dead_letter_queue_table_count'},{False},{False}
{'xdr_p2p_discovery_table_count'},{False},{False}
{'xdr_p2p_scanable_agents_count'},{False},{False}
{'xdr_phase1_installers_flag'},{False},{False}
{'xdr_platform_migration_completed'},{False},{False}
{'xdr_platform_migration_failed'},{False},{False}
{'xdr_platform_migration_start'},{False},{False}
{'xdr_platform_migration_status'},{False},{False}
{'xdr_prisma_pairing_status'},{False},{False}
{'xdr_pz_schema_waitress_occupied_threads'},{False},{False}
{'xdr_queries_by_status_count'},{False},{False}
{'xdr_redis_.+'},{False},{False}
{'xdr_reports_generator_.+'},{False},{False}
{'xdr_request_processing_seconds_count'},{True},{True}
{'xdr_request_processing_seconds_sum'},{True},{True}
{'xdr_request_response_size_count'},{True},{False}
{'xdr_request_response_size_sum'},{True},{False}
{'xdr_retention_enforcement_status'},{False},{False}
{'xdr_retention_enforcement_task_run'},{False},{False}
{'xdr_retention_service_sync_retention_stats_failures_total'},{True},{False}
{'xdr_retention_simulation_status'},{False},{False}
{'xdr_retryable_grouping_alerts_.+'},{False},{False}
{'xdr_sbac_enabled'},{False},{False}
{'xdr_sbac_enabled.+'},{False},{False}
{'xdr_sbac_mode'},{False},{False}
{'xdr_schd_task_delay_histogram_bucket'},{False},{False}
{'xdr_scheduler_wlm_working'},{True},{False}
{'xdr_search_index.+'},{False},{False}
{'xdr_story_builder_cpu_utilization'},{False},{False}
{'xdr_story_builder_max_delay'},{True},{False}
{'xdr_scouter_to_group_calculation_count'},{True},{False}
{'xdr_scouter_to_group_calculation_duration'},{True},{False}
{'xdr_stuck_triage_records'},{False},{False}
{'xdr_tags_count'},{False},{False}
{'xdr_tags_sync_not_running'},{False},{False}
{'xdr_tenant_distribution_list'},{False},{False}
{'xdr_tim_.+'},{False},{False}
{'xdr_total_agents_by_content_status'},{False},{False}
{'xdr_total_size_of_stored_edr'},{False},{False}
{'xdr_unused_profile_count'},{False},{False}
{'xdr_upload_management_audit_to_gcs'},{False},{False}
{'xdr_users_access_to_tenant_count'},{False},{False}
{'xdr_users_login_to_tenant'},{False},{False}
{'xdr_va_.+'},{False},{False}
{'xdr_vulnerability_assessment_.+'},{False},{False}
{'xdr_wec_.+'},{False},{False}
{'xdr_wildfire_submit_url_low_priority_req_counter_total'},{False},{False}
{'xdr_num_of_disabled_rules'},{False},{False}
{'xdr_num_of_partially_disabled_rules'},{False},{False}
{'xdr_num_of_enabled_rules'},{False},{False}
{'xdr_platform_migration_tf_completed'},{False},{False}
{'xdr_platform_migration_tf_start'},{False},{False}
{'xdr_whitelist_activation_status'},{False},{False}
{'xdr_wildfire_submit_url_req_counter_total'},{False},{False}
{'xdr_wildfire_submit_url_res_status_total'},{True},{False}
{'xdr_wlm_count_by_status'},{False},{False}
{'xdr_wlm_count_table_rows'},{False},{False}
{'xdr_wlm_oldest_task.+'},{False},{False}
{'xdr_wlm_pending_tasks.+'},{False},{False}
{'xdr_wlm_task_delay.+'},{False},{False}
{'xdr_wlm_task_received_total'},{False},{False}
{'xdr_wlm_task_started_total'},{False},{False}
{'xdr_xdr_license_count'},{False},{False}
{'xdr_xdr_license_expiration'},{False},{False}
{'xdr_xpanse_alerts_resolver_queue'},{False},{False}
{'xdr_xpanse_incident_context_injection_failure_total'},{True},{False}
{'xdr_xsiam_gb_license_count'},{False},{False}
{'xdr_xsiam_users_license_count'},{False},{False}
{'xpanse_alert_fetcher.+'},{False},{False}
{'xpanse_asset_tag_rules_error_total'},{True},{True}
{'xpanse_compliance_frameworks.+'},{False},{False}
{'xpanse_data_migration.+'},{False},{False}
{'xpanse_explorer_ratings_cache_error_total'},{True},{False}
{'xpanse_incident_context_injection_succeed_total'},{True},{False}
{'xpanse_integrations_data_monitoring_metrics_prisma_collectors_enabled_counts'},{True},{True}
{'xpanse_integrations_data_monitoring_metrics_prisma_collectors_failed_counts'},{True},{True}
{'xpanse_integrations_data_monitoring_metrics_xcloud_collectors_enabled_counts'},{True},{True}
{'XPANSE_MANUAL_SCAN_REQUEST_FAILED_COUNTER_total'},{True},{False}
{'XPANSE_MANUAL_SCAN_REQUESTED_COUNT_total'},{True},{False}
{'XPANSE_WEBSITES_DO_NOT_EXIST_ERROR_total'},{True},{False}
{'XPANSE_WEBSITE_FAILED_LOADING_ITEM_ERROR_total'},{True},{False}
{'XPANSE_WEBSITE_DETAILS_UNEXPECTED_RESULT_total'},{True},{False}
{'xpanse_policies.+'},{False},{False}
{'xpanse_policy.+'},{False},{False}
{'xpanse_rcs_result_processor_results_processed_count_total'},{True},{False}
{'xpanse_score_recalculation.+'},{False},{False}
{'xpanse_scoring_context.+'},{False},{False}
{'xpanse_tags_to_mysql_sync_error_total'},{True},{False}
{'xpanse_technology_metadata.+'},{False},{False}
{'xpanse_threat_events.+'},{False},{False}
{'xpanse_websites.+'},{False},{False}
{'xpanse_widget.+'},{False},{False}
{'xql_.+'},{False},{False}
{'xsoar_archive_error'},{False},{False}
{'xsoar_silent_playbooks_counter'},{False},{False}
{'xsoar_auto_extract_enrich_all_indicators_bucket'},{True},{True}
{'xsoar_auto_extract_enrich_indicator_command_bucket'},{True},{True}
{'xsoar_auto_extract_entry_processing_bucket'},{True},{True}
{'xsoar_auto_extract_find_indicator_bucket'},{True},{True}
{'xsoar_auto_extract_format_indicator_bucket'},{True},{True}
{'xsoar_auto_extract_indicator_enriched_command'},{False},{False}
{'xsoar_auto_extract_indicator_enrichment_timeout'},{False},{False}
{'xsoar_auto_extract_indicator_extracted'},{False},{False}
{'xsoar_auto_extract_indicator_formatted'},{False},{False}
{'xsoar_auto_extract_indicator_mapped'},{False},{False}
{'xsoar_auto_extract_map_indicator_bucket'},{True},{True}
{'xsoar_automation_executions_avg_duration_seconds'},{False},{False}
{'xsoar_automation_executions_count'},{False},{False}
{'xsoar_command_executions_avg_duration_seconds'},{False},{False}
{'xsoar_command_executions_count'},{False},{False}
{'xsoar_content_backup_snapshot_status'},{False},{False}
{'xsoar_engine_disconnections'},{False},{False}
{'xsoar_engine_health_status'},{False},{False}
{'xsoar_engine_last_health_timestamp'},{False},{False}
{'xsoar_fetch_execution_fetch_duration_seconds'},{False},{False}
{'xsoar_fetch_execution_health_status'},{False},{False}
{'xsoar_fetch_execution_ingestion_duration_seconds'},{False},{False}
{'xsoar_fetch_execution_pulled_incidents_count'},{False},{False}
{'xsoar_fetch_execution_pulled_indicators_count'},{False},{False}
{'xsoar_ha_lock_hijacks_total'},{False},{False}
{'xsoar_ha_mirroring_error'},{False},{False}
{'xsoar_ha_secondary_pb_executions_total'},{False},{False}
{'xsoar_migration_status'},{False},{False}
{'xsoar_msg_bus_publisher'},{True},{True}
{'xsoar_msg_bus_messages'},{False},{False}
{'xsoar_msg_bus_total_active'},{False},{False}
{'xsoar_msg_bus_oldest'},{False},{False}
{'xsoar_msg_bus_oldest_nth'},{False},{False}
{'xsoar_msg_bus_oldest_unhandled_nth'},{False},{False}
{'xsoar_msg_bus_total'},{False},{False}
{'xsoar_msg_bus_max_hpa'},{False},{False}
{'xsoar_msg_bus_min_hpa'},{False},{False}
{'xsoar_msg_bus_replicas'},{False},{False}
{'xsoar_msg_bus_reserved_replicas'},{False},{False}
{'xsoar_msg_bus_subscriber_pull'},{True},{True}
{'xsoar_msg_bus_subscriber_ack'},{True},{True}
{'xsoar_msg_bus_subscriber_nack'},{True},{True}
{'xsoar_msg_bus_dlq_subscriber_pull'},{True},{True}
{'xsoar_msg_bus_lock_latency_bucket'},{True},{True}
{'xsoar_msg_bus_insert_latency_bucket'},{True},{True}
{'xsoar_msg_bus_subscriber_pull_latency_bucket'},{True},{True}
{'xsoar_msg_bus_subscriber_ack_latency_bucket'},{True},{True}
{'xsoar_msg_bus_subscriber_nack_latency_bucket'},{True},{True}
{'xsoar_msg_bus_fetch_metrics_latency_bucket'},{True},{True}
{'xsoar_msg_bus_oldest_unhandled'},{False},{False}
{'xsoar_num_archived'},{False},{False}
{'xsoar_num_data_loss_alert'},{False},{False}
{'xsoar_num_integrations_updated'},{False},{False}
{'xsoar_num_of_current_integrations_installed'},{False},{False}
{'xsoar_num_oversized_alert'},{False},{False}
{'xsoar_num_panics'},{False},{False}
{'xsoar_num_sql_errors'},{False},{False}
{'xsoar_num_un_archived'},{False},{False}
{'xsoar_num_warroom_errors'},{False},{False}
{'xsoar_reminder_queue_push_error'},{False},{False}
{'xsoar_reminder_queue_counter'},{False},{False}
{'xsoar_reminder_queue_timed_out_entries'},{False},{False}
{'xsoar_stuck_inv_playbook_detected_total'},{False},{False}
{'xsoar_un_archive_error'},{False},{False}
{'xsoar_completed_inv_playbook_total'},{False},{False}
{'xsoar_inv_playbook_ack_nack_messages_total'},{False},{False}
{'workflow_failed'},{True},{True}
{'workflow_success'},{True},{True}
{'workflow_timeout'},{True},{True}
{'persistence_requests'},{True},{True}
{'logback_events_total'},{True},{True}
{'mongo_polaris_inserts_total'},{True},{True}
{'controller_runtime_reconcile_panics_total'},{False},{False}
{'controller_runtime_reconcile_errors_total'},{False},{False}
{'controller_runtime_terminal_reconcile_errors_total'},{False},{False}
