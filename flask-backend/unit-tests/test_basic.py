#!/usr/bin/env python3
"""
Basic test to verify test structure works without Flask dependencies
"""

import unittest
import os
import sys


class TestBasicStructure(unittest.TestCase):
    """Basic tests that don't require Flask dependencies"""

    def test_file_structure(self):
        """Test that required files exist"""
        # Go up one directory to flask-backend root
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Check that flask_startup.py exists
        flask_file = os.path.join(current_dir, 'flask_startup.py')
        self.assertTrue(os.path.exists(flask_file), "flask_startup.py should exist")
        
        # Check that test files exist in unit-tests directory
        test_dir = os.path.dirname(os.path.abspath(__file__))
        test_files = [
            'test_flask_startup_fixed.py',
            'test_basic.py'
        ]
        
        for test_file in test_files:
            file_path = os.path.join(test_dir, test_file)
            self.assertTrue(os.path.exists(file_path), f"{test_file} should exist in unit-tests/")

    def test_python_version(self):
        """Test that Python version is compatible"""
        version = sys.version_info
        self.assertGreaterEqual(version.major, 3, "Python 3 is required")
        self.assertGreaterEqual(version.minor, 6, "Python 3.6+ is required")

    def test_import_basic_modules(self):
        """Test that basic Python modules can be imported"""
        try:
            import json
            import unittest
            import os
            import sys
            from unittest.mock import patch, MagicMock
        except ImportError as e:
            self.fail(f"Failed to import basic modules: {e}")

    def test_flask_startup_file_content(self):
        """Test that flask_startup.py has expected content"""
        # Go up one directory to flask-backend root
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        flask_file = os.path.join(current_dir, 'flask_startup.py')
        
        with open(flask_file, 'r') as f:
            content = f.read()
        
        # Check for expected endpoints
        self.assertIn('/process_data', content, "Should have /process_data endpoint")
        self.assertIn('/create_jira_issue', content, "Should have /create_jira_issue endpoint")
        
        # Check for expected imports
        self.assertIn('from flask import', content, "Should import Flask")
        self.assertIn('Flask(__name__)', content, "Should create Flask app")

    def test_requirements_file_exists(self):
        """Test that requirements files exist"""
        # Go up one directory to flask-backend root
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # Check for test requirements
        test_req_file = os.path.join(current_dir, 'unit-tests', 'test_requirements.txt')
        if os.path.exists(test_req_file):
            with open(test_req_file, 'r') as f:
                content = f.read()
                self.assertIn('pytest', content, "Should include pytest in test requirements")


class TestDependencyCheck(unittest.TestCase):
    """Tests to check if dependencies are available"""

    def test_flask_availability(self):
        """Check if Flask is available"""
        try:
            import flask
            self.assertTrue(True, "Flask is available")
        except ImportError:
            self.skipTest("Flask not available - install with: pip install flask")

    def test_flask_cors_availability(self):
        """Check if Flask-CORS is available"""
        try:
            import flask_cors
            self.assertTrue(True, "Flask-CORS is available")
        except ImportError:
            self.skipTest("Flask-CORS not available - install with: pip install flask-cors")

    def test_jira_availability(self):
        """Check if Jira libraries are available"""
        try:
            import jira
            import atlassian
            self.assertTrue(True, "Jira libraries are available")
        except ImportError:
            self.skipTest("Jira libraries not available - install with: pip install jira atlassian-python-api")

    def test_requests_availability(self):
        """Check if requests is available"""
        try:
            import requests
            self.assertTrue(True, "Requests is available")
        except ImportError:
            self.skipTest("Requests not available - install with: pip install requests")


class TestInstallationInstructions(unittest.TestCase):
    """Tests that provide installation instructions"""

    def test_show_installation_instructions(self):
        """Show installation instructions for missing dependencies"""
        missing_deps = []
        
        try:
            import flask
        except ImportError:
            missing_deps.append("flask")
        
        try:
            import flask_cors
        except ImportError:
            missing_deps.append("flask-cors")
        
        try:
            import requests
        except ImportError:
            missing_deps.append("requests")
        
        try:
            import jira
        except ImportError:
            missing_deps.append("jira")
        
        try:
            import atlassian
        except ImportError:
            missing_deps.append("atlassian-python-api")
        
        if missing_deps:
            install_cmd = f"pip install {' '.join(missing_deps)}"
            print(f"\n{'='*60}")
            print("MISSING DEPENDENCIES DETECTED")
            print(f"{'='*60}")
            print(f"To run the full test suite, install missing dependencies:")
            print(f"  {install_cmd}")
            print(f"{'='*60}")
            
            # This test always passes, it's just informational
            self.assertTrue(True, "Installation instructions displayed")
        else:
            print("\n✅ All dependencies are available!")
            self.assertTrue(True, "All dependencies available")


if __name__ == '__main__':
    print("Running basic structure tests...")
    print("This test can run without Flask dependencies installed.")
    print("=" * 60)
    
    unittest.main(verbosity=2)
