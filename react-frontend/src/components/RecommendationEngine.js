import React from 'react';

function RecommendationEngine({
  metrics,
  recordingRules,
  onRecordingRuleChange, // <<< New prop
  alertExamples,
  onAlertExampleChange,
  isLoadingRules,
  isLoadingAlerts,
  cardinalityResults,
  selectedLabels // Keep this prop if used for any other logic, though not directly for rendering textareas here
}) {

  // Filter metrics that are not 'Fatal' for showing recommendations
  // OR metrics for which cardinality check hasn't run yet (if qaTenant not selected)
  const actionableMetrics = metrics.filter(m => {
    if (!cardinalityResults[m.id] && Object.keys(cardinalityResults).length < metrics.length) return true; // Show if results not yet loaded for this metric
    return cardinalityResults[m.id]?.status !== 'Fatalx';
  });


  if (actionableMetrics.length === 0 && metrics.length > 0 && Object.keys(cardinalityResults).length === metrics.length) {
      return (
           <section className="dashboard-section">
            <h2>5. Recommendations (Recording Rules & Alerts)</h2>
            <p className="message message-info">No metrics available for recommendations (e.g., all have Fatal issues).</p>
           </section>
      );
  }
  // Don't render section if no metrics at all, or if loading and no data yet
  if (metrics.length === 0 || (isLoadingRules && Object.keys(recordingRules).length === 0) || (isLoadingAlerts && Object.keys(alertExamples).length === 0)) {
      if (isLoadingRules || isLoadingAlerts) return <div className="dashboard-section"><div className="loading-spinner"></div></div>;
      return null;
  }


  return (
    <section className="dashboard-section">
      <h2>5. Recommendations (Recording Rules & Alerts)</h2>
      {(isLoadingRules && Object.keys(recordingRules).length === 0) && <div className="loading-spinner">Loading Rules...</div>}
      {(isLoadingAlerts && Object.keys(alertExamples).length === 0) && <div className="loading-spinner">Loading Alerts...</div>}

      {actionableMetrics.map(metric => {
        const ruleInfo = recordingRules[metric.id];
        const alertInfo = alertExamples[metric.id];

        // Determine if this metric block has any content to show
        const hasRuleContent = ruleInfo && (ruleInfo.example || ruleInfo.needed);
        const hasAlertContent = alertInfo && (alertInfo.alerts || alertInfo.message);

        if (!hasRuleContent && !hasAlertContent) return null; // Skip if no info for this metric

        // Check if the rule example is the "not necessary" placeholder
        const isRuleNotNeededMessage = ruleInfo?.example && ruleInfo.example.includes("not be necessary");

        return (
          <div key={`reco-${metric.id}`} className="metric-recommendation-block">
            {hasRuleContent && (
              <div className="recommendation">
                <h4>Recording Rule Suggestion for: {metric.name} (Editable)</h4>
                {ruleInfo.needed && (
                  <p className="message message-info">
                    A recording rule was initially suggested for this metric. Please review or define the rule below. <strong>SRE review is required.</strong>
                  </p>
                )}
                {isRuleNotNeededMessage ? (
                  <p className="message message-ok">{ruleInfo.example}</p>
                ) : (
                  // Show textarea if an example exists (and it's not the "not necessary" message)
                  // OR if a rule is needed (allowing user to fill it from scratch)
                  // OR if an example exists (even if not 'needed', allowing edit of a prefill)
                  (ruleInfo.example || ruleInfo.needed) && (
                    <textarea
                      className="editable-rule-yaml"
                      value={ruleInfo.example && !isRuleNotNeededMessage ? ruleInfo.example : ""}
                      onChange={(e) => onRecordingRuleChange(metric.id, e.target.value)}
                      rows={10}
                      spellCheck="false"
                      placeholder={
                        ruleInfo.needed
                          ? "Enter or modify recording rule YAML here..."
                          : "Define recording rule if desired (not initially suggested)..."
                      }
                    />
                  )
                )}
              </div>
            )}

            {hasAlertContent && (
              <div className="recommendation alert-recommendation">
                <h4>Alert Examples for: {metric.name} (Editable)</h4>
                {alertInfo.message && (
                  <p className="message message-info">{alertInfo.message}</p>
                )}
                <textarea
                  className="editable-alert-yaml"
                  value={alertInfo.alerts || ""}
                  onChange={(e) => onAlertExampleChange(metric.id, e.target.value)}
                  rows={12}
                  spellCheck="false"
                  placeholder="Enter or modify alert YAML here..."
                />
              </div>
            )}
          </div>
        );
      })}
    </section>
  );
}

export default RecommendationEngine;